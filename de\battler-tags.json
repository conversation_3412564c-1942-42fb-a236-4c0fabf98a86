{"trappedDesc": "Wechselsperre", "flinchedDesc": "Zurückschrecken", "confusedDesc": "Verwirrung", "infatuatedDesc": "Verliebt", "seedDesc": "Bepflanzt", "nightmareDesc": "Nachtmahr", "ingrainDesc": "Verwurzlung", "drowsyDesc": "Schläfrigkeit", "rechargingLapse": "{{pokemonNameWithAffix}} kann sich wegen des Rückstoßes durch den Angriff nicht bewegen!", "trappedOnAdd": "{{pokemonNameWithAffix}} kann nicht mehr fliehen!", "trappedOnRemove": "{{pokemonNameWithAffix}} wurde von {{moveName}} befreit.", "flinchedLapse": "{{pokemonNameWithAffix}} ist zurückgeschreckt und kann nicht handeln!", "confusedOnAdd": "{{pokemonNameWithAffix}} wurde verwirrt!", "confusedOnRemove": "{{pokemonNameWithAffix}} ist nicht mehr verwirrt!", "confusedOnOverlap": "{{pokemonNameWithAffix}} ist bereits verwirrt!", "confusedLapse": "{{pokemonNameWithAffix}} ist verwirrt!", "confusedLapseHurtItself": "Es hat sich vor Verwirrung selbst verletzt!", "destinyBondLapseIsBoss": "{{pokemonNameWithAffix}} ist immun gegen den Effekt von Abgangsbund!", "destinyBondLapse": "{{pokemonNameWithAffix}} nimmt {{pokemonNameWithAffix2}} mit sich!", "laserFocusOnAdd": "{{pokemonNameWithAffix}} schärft seine Sinne!", "infatuatedOnAdd": "{{pokemonNameWithAffix}} hat sich in {{sourcePokemonName}} verliebt!", "infatuatedOnOverlap": "{{pokemonNameWithAffix}} ist bereits verliebt.", "infatuatedLapse": "{{pokemonNameWithAffix}} ist in {{sourcePokemonName}} verliebt!", "infatuatedLapseImmobilize": "{{pokemonNameWithAffix}} ist starr vor Liebe!", "infatuatedOnRemove": "{{pokemonNameWithAffix}} ist nicht mehr verliebt!", "seededOnAdd": "{{pokemonNameWithAffix}} wurde bepflanzt!", "seededLapse": "{{pokemonNameWithAffix}} wurden durch Egelsamen KP geraubt!", "seededLapseShed": "<PERSON><PERSON>same<PERSON> von {{pokemonNameWithAffix}} saugt Kloakensoße auf!", "nightmareOnAdd": "Nachtmahr sucht {{pokemonNameWithAffix}} heim!", "nightmareOnOverlap": "{{pokemonNameWithAffix}} wird bereits von Nachtmahr heimgesucht!", "nightmareLapse": "Nachtmahr schadet {{pokemonNameWithAffix}}!", "encoreOnAdd": "{{pokemonNameWithAffix}} gibt eine <PERSON>uga<PERSON>", "encoreOnRemove": "Die Zugabe von {{pokemonNameWithAffix}} ist beendet!", "helpingHandOnAdd": "{{pokemonNameWithAffix}} will {{pokemonName}} helfen!", "ingrainLapse": "{{pokemonNameWithAffix}} nimmt über seine Wurzeln Nährstoffe auf!", "ingrainOnTrap": "{{pokemonNameWithAffix}} pflanzt seine Wurzeln!", "aquaRingOnAdd": "{{pokemonNameWithAffix}} umgibt sich mit einem <PERSON>!", "aquaRingLapse": "{{moveName}} füllt KP von {{pokemonName}} wieder auf!", "drowsyOnAdd": "{{pokemonNameWithAffix}} wurde schläfrig gemacht!", "damagingTrapLapse": "{{pokemonNameWithAffix}} wurde durch {{moveName}} verletzt!", "bindOnTrap": "{{pokemonNameWithAffix}} wurde durch {{moveName}} von {{sourcePokemonName}} gequetscht!", "wrapOnTrap": "{{pokemonNameWithAffix}} wurde von {{sourcePokemonName}} umwickelt!", "vortexOnTrap": "{{pokemonNameWithAffix}} wird in dem Strudel gefangen!", "clampOnTrap": "{{sourcePokemonNameWithAffix}} wurde von {{pokemonName}} geschnappt!", "sandTombOnTrap": "{{pokemonNameWithAffix}} wurde von {{moveName}} gefangen!", "magmaStormOnTrap": "{{pokemonNameWithAffix}} wurde in wirbelndem Magma eingeschlossen!", "snapTrapOnTrap": "{{pokemonNameWithAffix}} wurde durch Sandgrab gefangen!", "thunderCageOnTrap": "{{sourcePokemonNameWithAffix}} hat {{pokemonNameWithAffix}} gefangen!", "infestationOnTrap": "{{sourcePokemonNameWithAffix}} plagt {{pokemonNameWithAffix}}!", "protectedOnAdd": "{{pokemonNameWithAffix}} schützt sich selbst!", "protectedLapse": "{{pokemonNameWithAffix}} schützt sich selbst!", "enduringOnAdd": "{{pokemonNameWithAffix}} sammelt sich, um die nächste Attacke zu überstehen!", "enduringLapse": "{{pokemonNameWithAffix}} übersteht die Attacke!", "sturdyLapse": "{{pokemonNameWithAffix}} übersteht die Attacke!", "perishSongLapse": "Abges<PERSON> von {{pokemonNameWithAffix}} steht bei {{turnCount}}.", "centerOfAttentionOnAdd": "{{pokemonNameWithAffix}} zieht alle Aufmerksamkeit auf sich!", "truantLapse": "{{pokemonNameWithAffix}} faulenzt!", "slowStartOnAdd": "{{pokemonNameWithAffix}} kommt nicht in Fahrt!", "slowStartOnRemove": "{{pokemonNameWithAffix}} kriegt schließlich doch noch die Kurve!", "highestStatBoostOnAdd": "{{statName}} von {{pokemonNameWithAffix}} wird verstärkt!", "highestStatBoostOnRemove": "Der Effekt von {{abilityName}} von {{pokemonNameWithAffix}} lässt nach!", "magnetRisenOnAdd": "{{pokemonNameWithAffix}} schwebt aufgrund von Elektromagnetismus!", "magnetRisenOnRemove": "Der Elektromagnetismus von {{pokemonNameWithAffix}} hört auf zu wirken!", "critBoostOnAdd": "{{pokemonNameWithAffix}} läuft zu Hochtouren auf!", "critBoostOnRemove": "{{pokemonNameWithAffix}} entspannt.", "saltCuredOnAdd": "{{pokemonNameWithAffix}} wurde eingepökelt!", "saltCuredLapse": "{{pokemonNameWithAffix}} wurde durch {{moveName}} verletzt!", "cursedOnAdd": "{{pokemonNameWithAffix}} nimmt einen Teil seiner KP und legt einen Fluch auf {{pokemonName}}!", "cursedLapse": "{{pokemonNameWithAffix}} wurde durch den Fluch verletzt!", "stockpilingOnAdd": "{{pokemonNameWithAffix}} hortet {{stockpiledCount}}!", "disabledOnAdd": " {{moveName}} von {{pokemonNameWithAffix}} wurde blockiert!", "disabledLapse": "{{moveName}} von {{pokemonNameWithAffix}} ist nicht länger blockiert!", "tarShotOnAdd": "{{pokemonNameWithAffix}} ist nun schwach gegenüber Feuer-Attacken!", "shedTailOnAdd": "{{pokemonNameWithAffix}} wirft seinen <PERSON>nz ab, um eine Ablenkung zu schaffen!", "substituteOnAdd": "Ein Delegator von {{pokemonNameWithAffix}} ist erschienen!", "substituteOnHit": "Der Delegator steckt den Schlag für {{pokemonNameWithAffix}} ein!", "substituteOnRemove": "Der Delegator von {{pokemonNameWithAffix}} hört auf zu wirken!", "tormentOnAdd": "{{pokemonNameWithAffix}} wird der Attacke Folterknecht unterworfen!", "tauntOnAdd": "{{pokemonNameWithAffix}} fällt auf V<PERSON>höhner herein!", "tauntOnRemove": "<PERSON><PERSON>höhner wirkt nicht mehr auf {{pokemonNameWithAffix}}!", "imprisonOnAdd": "{{pokemonNameWithAffix}} versiegelt jene gegnerischen Attacken, die es selbst auch beherrscht!", "autotomizeOnAdd": "{{pokemonNameWithAffix}} ist leichter geworden!", "syrupBombOnAdd": "{{pokemonNameWithAffix}} wurde in Sirup gehüllt!!", "syrupBombLapse": "Der Sirup verlangsamt {{pokemonNameWithAffix}}!", "telekinesisOnAdd": "{{pokemonNameWithAffix}} wurde zum Schweben gebracht!", "electrifiedOnAdd": "Die nächste Attacke von {{pokemonNameWithAffix}} wird elektrifiziert!", "powerTrickActive": "{{pokemonNameWithAffix}} tauscht den Wert seines Angriffs mit dem seiner Verteidigung!", "powderOnAdd": "Auf {{pokemonNameWithAffix}} wurde Pulver geschleudert!", "powderLapse": "{{moveName}} bringt das Pulver zum Explodieren!", "grudgeOnAdd": "{{pokemonNameWithAffix}} m<PERSON><PERSON><PERSON>, dass der Gegner ein Nachspiel erträgt!", "grudgeLapse": "{{moveName}} von {{pokemonNameWithAffix}} hat durch Nachspiel alle AP verloren!", "magicCoatOnAdd": "{{pokemonNameWithAffix}} hüllt sich selbst in einen Magiemantel!"}