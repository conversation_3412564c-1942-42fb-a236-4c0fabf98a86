{"intro": "Ein sehr starker Trainer kommt auf dich zu…", "buck": {"intro_dialogue": "Yo, Trainer! Mein Name ist Avenaro.$Ich habe ein super Angebot für einen starken Trainer wie dich!$Ich trage zwei seltene Pokémon-Eier bei mir, aber ich möchte, dass sich jemand anderes um eines kümmert.$Wenn du mir beweisen kannst, dass du ein starker Trainer bist, werde ich dir das seltenere Ei geben!", "accept": "Wohooo! Ich bin Feuer und Flamme!", "decline": "<PERSON><PERSON>, es sieht so aus, als wäre dein Team nicht in Bestform.$<PERSON>, lass mich dir helfen."}, "cheryl": {"intro_dialogue": "Hallo mein Name ist Raissa, ich habe eine besondere Bitte an dich, einen starken Trainer.$Ich trage zwei seltene Pokémon-Eier bei mir, aber ich möchte, dass sich jemand anderes um eines kümmert.$Wenn du mir beweisen kannst, dass du ein starker Trainer bist, werde ich dir das seltenere Ei geben!", "accept": "<PERSON>ch hoffe, du bist bereit!", "decline": "<PERSON><PERSON> verstehe, es sieht so aus, als wäre dein Team nicht in der besten Verfassung.$<PERSON><PERSON>, lass mich dir helfen."}, "marley": {"intro_dialogue": "…@d{64} <PERSON>ch bin Charlie.$Ich habe ein Angebot für dich…$Ich trage zwei Pokémon-Eier bei mir, aber ich möchte, dass sich jemand anderes um eines kümmert.$Wenn du stärker bist als ich, werde ich dir das seltenere E<PERSON> geben.", "accept": "…So ist das also.", "decline": "…Deine Pokémon sehen verletzt aus…Lass mich helfen."}, "mira": {"intro_dialogue": "Hi, ich bin Orisa!$Ich habe eine Bitte an dich, einen starken Trainer.$Ich trage zwei seltene Pokémon-Eier bei mir, aber ich möchte, dass sich jemand anderes um eines kümmert.$Wenn du mir beweisen kannst, dass du ein starker Trainer bist, werde ich dir das seltenere Ei geben!", "accept": "Du wirst <PERSON><PERSON>? <PERSON><PERSON>!", "decline": "Aww, kein <PERSON>? Das ist okay!$<PERSON><PERSON>, <PERSON><PERSON> wird dein Team heilen!"}, "riley": {"intro_dialogue": "<PERSON><PERSON>, ich habe eine Bit<PERSON> an dich, einen starken Trainer.$Ich trage zwei seltene Pokémon-Eier bei mir, aber ich möchte, dass sich jemand anderes um eines kümmert.$Wenn du mir beweisen kannst, dass du ein starker Trainer bist, werde ich dir das seltenere Ei geben!", "accept": "<PERSON><PERSON>…<PERSON> un<PERSON> das machen.", "decline": "<PERSON><PERSON>, dein Team sieht geschlagen aus.$<PERSON>, lass mich dir helfen."}, "title": "<PERSON> Trainer-Test", "description": "<PERSON>s scheint als würde dieser Trainer dir ein Ei geben, egal wie du dich entscheidest. Wenn du es jedoch scha<PERSON>, diesen starken Trainer zu besiegen, wirst du ein viel selteneres Ei erhalten.", "query": "Was wirst du tun?", "option": {"1": {"label": "Die Herausforderung annehmen", "tooltip": "(-) <PERSON><PERSON><PERSON><PERSON> Ka<PERSON>\n(+) <PERSON><PERSON><PERSON><PERSON> ein @[TOOLTIP_TITLE]{Sehr seltenes Ei}"}, "2": {"label": "Die Herausforderung ablehnen", "tooltip": "(+) Team wird geheilt\n(+) <PERSON><PERSON><PERSON><PERSON> ein @[TOOLTIP_TITLE]{Ei}"}}, "eggTypes": {"rare": "seltenes Ei", "epic": "episches Ei", "legendary": "<PERSON><PERSON><PERSON>"}, "outro": "{{statTrainerName}} gibt dir ein {{eggType}}!"}