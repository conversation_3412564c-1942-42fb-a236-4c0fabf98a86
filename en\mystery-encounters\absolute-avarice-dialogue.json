{"intro": "A {{greedent<PERSON><PERSON>}} ambushes you\nand steals your party’s berries!", "title": "Absolute Avarice", "description": "The {{greedentName}} has caught you totally off guard now all your berries are gone!\n\nThe {{greedentName}} looks like it’s about to eat them when it pauses to look at you, interested.", "query": "What will you do?", "option": {"1": {"label": "Battle It", "tooltip": "(-) Tough Battle\n(+) Rewards from its Berry Hoard", "selected": "The {{greedent<PERSON>ame}} stuffs its cheeks\nand prepares for battle!", "boss_enraged": "{{greedent<PERSON>ame}}’s fierce love for food has it incensed!", "food_stash": "It looks like the {{greedentName}} was guarding an enormous stash of food!$@s{item_fanfare}Each Pokémon in your party gains a {{foodReward}}!"}, "2": {"label": "Reason with It", "tooltip": "(+) <PERSON><PERSON> Some Lost Berries", "selected": "Your pleading strikes a chord with the {{greedentName}}.$It doesn’t give all your berries back, but still tosses a few in your direction."}, "3": {"label": "Let It Have the Food", "tooltip": "(-) <PERSON>e All Berries\n(?) The {{greedentName}} Will Like You", "selected": "The {{greedentName}} devours the entire\nstash of berries in a flash!$Patting its stomach,\nit looks at you appreciatively.$Perhaps you could feed it\nmore berries on your adventure…$@s{level_up_fanfare}The {{greedentName}} wants to join your party!"}}}