{"ace_trainer": "Ass-Trainer", "ace_trainer_female": "Ass-<PERSON><PERSON><PERSON>", "ace_duo": "Ass-Duo", "artist": "<PERSON><PERSON><PERSON><PERSON>", "artist_female": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backers": "<PERSON><PERSON><PERSON><PERSON>", "backpacker": "Backpacker", "backpacker_female": "Backpackerin", "backpackers": "Backpacker", "baker": "<PERSON><PERSON><PERSON><PERSON>", "battle_girl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beauty": "Schönheit", "beginners": "<PERSON><PERSON><PERSON><PERSON>", "biker": "Rowdy", "black_belt": "Schwarz<PERSON>rt", "breeder": "<PERSON>", "breeder_female": "Pokémon Züchterin", "breeders": "<PERSON>", "clerk": "<PERSON><PERSON><PERSON><PERSON>", "clerk_female": "Angestellte", "colleagues": "Geschäftspartner", "crush_kin": "M<PERSON><PERSON>ens<PERSON><PERSON>", "cyclist": "Biker", "cyclist_female": "<PERSON><PERSON><PERSON>", "cyclists": "Biker", "dancer": "<PERSON><PERSON><PERSON><PERSON>", "dancer_female": "Tänzerin", "depot_agent": "Bahnangestellter", "doctor": "Arz<PERSON>", "doctor_female": "<PERSON><PERSON><PERSON>", "firebreather": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fisherman": "<PERSON><PERSON>", "fisherman_female": "<PERSON><PERSON>", "gentleman": "Gentleman", "guitarist": "<PERSON><PERSON><PERSON><PERSON>", "guitarist_female": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harlequin": "<PERSON><PERSON>", "hiker": "<PERSON><PERSON><PERSON>", "hooligans": "Rabauken", "hoopster": "Basketballer", "infielder": "Baseballer", "janitor": "<PERSON><PERSON><PERSON>", "lady": "Lady", "lass": "<PERSON><PERSON><PERSON>", "linebacker": "Footballer", "maid": "Zofe", "madame": "<PERSON><PERSON>", "medical_team": "Mediziner", "musician": "<PERSON><PERSON><PERSON>", "hex_maniac": "Hexe", "nurse": "<PERSON><PERSON><PERSON><PERSON>", "nursery_aide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "officer": "Polizist", "parasol_lady": "Schirm<PERSON><PERSON>", "pilot": "Pilot", "pokéfan": "<PERSON><PERSON><PERSON><PERSON>", "pokéfan_female": "<PERSON><PERSON><PERSON><PERSON>", "pokéfan_family": "Pokéfan-Pärchen", "preschooler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preschooler_female": "Vorschü<PERSON><PERSON>", "preschoolers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "psychic": "<PERSON><PERSON>", "psychic_female": "<PERSON><PERSON><PERSON>", "psychics": "<PERSON><PERSON>", "pokémon_ranger": "Pokémon-Ranger", "pokémon_ranger_female": "Pokémon-Ranger", "pokémon_rangers": "Pokémon-Ranger", "ranger": "<PERSON>", "restaurant_staff": "Restaurant Angestellte", "rich": "Gentleman", "rich_female": "<PERSON><PERSON>", "rich_boy": "<PERSON><PERSON><PERSON><PERSON>", "rich_couple": "<PERSON><PERSON>", "rich_kid": "<PERSON><PERSON><PERSON><PERSON>", "rich_kid_female": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rich_kids": "<PERSON><PERSON><PERSON><PERSON>", "roughneck": "<PERSON><PERSON><PERSON><PERSON>", "sailor": "<PERSON><PERSON>", "scientist": "<PERSON><PERSON>", "scientist_female": "For<PERSON><PERSON>", "scientists": "<PERSON><PERSON>", "smasher": "Tennis-Ass", "snow_worker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snow_worker_female": "Schneearbei<PERSON>in", "striker": "Fußballer", "school_kid": "<PERSON><PERSON><PERSON>", "school_kid_female": "<PERSON><PERSON><PERSON>", "school_kids": "<PERSON><PERSON><PERSON><PERSON>", "swimmer": "Schwimmer", "swimmer_female": "Schwi<PERSON>in", "swimmers": "Schwimmerpaar", "twins": "Zwillinge", "veteran": "Veteran", "veteran_female": "Veteran", "veteran_duo": "Veteranen", "waiter": "<PERSON><PERSON><PERSON>", "waitress": "Serviererin", "worker": "<PERSON><PERSON><PERSON><PERSON>", "worker_female": "Arbeiterin", "workers": "<PERSON><PERSON><PERSON><PERSON>", "youngster": "K<PERSON><PERSON>", "rocket_grunt": "<PERSON><PERSON><PERSON> von Team Rocket", "rocket_grunt_female": "<PERSON><PERSON><PERSON> von Team Rocket", "rocket_grunts": "<PERSON><PERSON><PERSON> von Team Rocket", "magma_grunt": "<PERSON><PERSON><PERSON> von Team Magma", "magma_grunt_female": "<PERSON><PERSON><PERSON> von Team Magma", "magma_grunts": "<PERSON><PERSON><PERSON> von Team Magma", "aqua_grunt": "<PERSON><PERSON><PERSON> von <PERSON>", "aqua_grunt_female": "<PERSON><PERSON><PERSON> von <PERSON>", "aqua_grunts": "<PERSON><PERSON><PERSON> von <PERSON>", "galactic_grunt": "<PERSON><PERSON><PERSON> von Team Galaktik", "galactic_grunt_female": "<PERSON><PERSON><PERSON> von Team Galaktik", "galactic_grunts": "<PERSON><PERSON><PERSON> von Team Galaktik", "plasma_grunt": "<PERSON><PERSON><PERSON> von Team Plasma", "plasma_grunt_female": "<PERSON><PERSON><PERSON> von Team Plasma", "plasma_grunts": "<PERSON><PERSON><PERSON> von Team Plasma", "flare_grunt": "<PERSON><PERSON><PERSON> von Team Flare", "flare_grunt_female": "<PERSON><PERSON><PERSON> von Team Flare", "flare_grunts": "<PERSON><PERSON><PERSON> von Team Flare", "aether_grunt": "Æther Foundation Personal", "aether_grunt_female": "Æther Foundation Personal", "aether_grunts": "Æther Foundation Personal", "skull_grunt": "<PERSON><PERSON><PERSON> von Team Skull", "skull_grunt_female": "<PERSON><PERSON><PERSON> von Team Skull", "skull_grunts": "<PERSON><PERSON><PERSON> von Team Skull", "macro_grunt": "<PERSON><PERSON><PERSON><PERSON> von Macro Cosmos", "macro_grunt_female": "<PERSON><PERSON><PERSON><PERSON> von Macro Cosmos", "macro_grunts": "<PERSON><PERSON><PERSON><PERSON> von Macro Cosmos", "star_grunt": "<PERSON><PERSON><PERSON> von Team Star", "star_grunt_female": "<PERSON><PERSON><PERSON> von Team Star", "star_grunts": "<PERSON><PERSON><PERSON> von Team Star"}