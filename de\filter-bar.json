{"genFilter": "Gen.", "typeFilter": "<PERSON><PERSON>", "biomeFilter": "Biome", "caughtFilter": "Gefang<PERSON>", "unlocksFilter": "<PERSON><PERSON><PERSON><PERSON>", "miscFilter": "Sonst.", "sortFilter": "Sort.", "all": "Alle", "normal": "Normal", "uncaught": "Nicht gefangen", "uncatchable": "Habitat unbekannt", "passive": "Passive", "passiveUnlocked": "Passive freigeschaltet", "passiveLocked": "Passive gesperrt", "passiveUnlockable": "Passive - Freischalten möglich", "costReduction": "Kostenreduzierung", "costReductionUnlocked": "Kosten bereits reduziert", "costReductionUnlockedOne": "Preisreduktion - Eins", "costReductionUnlockedTwo": "Preisreduktion - Zwei", "costReductionLocked": "Kosten noch nicht reduziert", "costReductionUnlockable": "Kosten können reduziert werden", "starter": "Starter", "isStarter": "Starter - <PERSON><PERSON>", "notStarter": "Starter <PERSON> <PERSON><PERSON>", "favorite": "<PERSON><PERSON><PERSON><PERSON>", "isFavorite": "Favorit - Ja", "notFavorite": "<PERSON>avori<PERSON> <PERSON> <PERSON><PERSON>", "ribbon": "Band", "hasWon": "Hat Klassik-<PERSON><PERSON> gewonnen", "hasNotWon": "Hat Klassik-Modus nicht gewonnen", "hiddenAbility": "Versteckte Fähigkeit", "hasHiddenAbility": "Versteckte Fähigkeit - Ja", "noHiddenAbility": "Versteckte Fähigkeit - Nein", "seenSpecies": "<PERSON><PERSON><PERSON><PERSON>", "isSeen": "<PERSON><PERSON><PERSON>", "isUnseen": "<PERSON>cht gesehen", "egg": "<PERSON>i", "eggPurchasable": "<PERSON><PERSON>", "pokerus": "Pokérus", "hasPokerus": "Pokérus - Ja", "noPokerus": "Pokérus - <PERSON>ein", "sortByNumber": "Pokédex-Nummer", "sortByCost": "<PERSON><PERSON>", "sortByCandies": "<PERSON><PERSON><PERSON>", "sortByIVs": "IS-Werte", "sortByName": "Name", "sortByNumCaught": "<PERSON><PERSON><PERSON> gefangen", "sortByNumHatched": "<PERSON><PERSON>hl ausgebrütet"}