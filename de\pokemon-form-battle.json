{"mega": "Mega-{{pokemon<PERSON><PERSON>}}", "mega-x": "Mega-{{pokemon<PERSON>ame}} X", "mega-y": "Mega-{{pokemon<PERSON>ame}} Y", "primal": "Proto-{{pokemon<PERSON><PERSON>}}", "gigantamax": "G-Dyna-{{pokemon<PERSON>ame}}", "gigantamax-single": "G-<PERSON>yna <PERSON>okussierter Stil {{pokemonName}}", "gigantamax-rapid": "G-Dyna Fließender Stil {{pokemonName}}", "eternamax": "U-Dyna-{{pokemon<PERSON>ame}}", "megaChange": "{{preName}} hat sich zu {{pokemonName}} mega-entwickelt!", "gigantamaxChange": "{{preName}} hat sich zu {{pokemonName}} gigadynamaximiert!", "eternamaxChange": "{{preName}} hat sich zu {{pokemonName}} unendynamaximiert!", "revertChange": "{{pokemon<PERSON>ame}} hat seine ursprüngliche Form zurückerlangt!", "formChange": "{{preName}} hat seine Form geändert!", "disguiseChange": "Sein Ko<PERSON>üm hat die Attacke absorbiert!"}