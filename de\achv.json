{"Achievements": {"name": "Errungenschaften"}, "Locked": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "MoneyAchv": {"description": "Häufe eine Gesamtsumme von {{moneyAmount}} ₽ an."}, "10K_MONEY": {"name": "Besserverdiener", "name_female": "Besserverdienerin"}, "100K_MONEY": {"name": "Reich", "name_female": "Reich"}, "1M_MONEY": {"name": "<PERSON><PERSON><PERSON>", "name_female": "<PERSON><PERSON><PERSON>"}, "10M_MONEY": {"name": "Einprozenter", "name_female": "Einprozenter"}, "DamageAchv": {"description": "Füge mit einem Treffer {{damageAmount}} <PERSON><PERSON><PERSON> zu."}, "250_DMG": {"name": "<PERSON><PERSON>"}, "1000_DMG": {"name": "<PERSON><PERSON><PERSON><PERSON> Treffer", "name_female": "<PERSON><PERSON><PERSON><PERSON> Treffer"}, "2500_DMG": {"name": "Das ist 'ne <PERSON>!", "name_female": "Das ist 'ne <PERSON>!"}, "10000_DMG": {"name": "One Punch Man", "name_female": "One Punch Woman"}, "HealAchv": {"description": "Heile {{heal<PERSON><PERSON>}} {{HP}} auf einmal mit einer Attacke, einer Fähigkeit oder einem gehaltenen Gegenstand."}, "250_HEAL": {"name": "Anfänger-Heiler", "name_female": "Anfänger-<PERSON><PERSON><PERSON>"}, "1000_HEAL": {"name": "Gesundheitsprofi", "name_female": "Gesundheitsprofi"}, "2500_HEAL": {"name": "<PERSON><PERSON><PERSON><PERSON>", "name_female": "<PERSON><PERSON><PERSON><PERSON>"}, "10000_HEAL": {"name": "<PERSON>iederherstellungsmeister", "name_female": "Wiederherstellungsmeisterin"}, "LevelAchv": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> das Level eines Pokémon auf {{level}}."}, "LV_100": {"name": "<PERSON><PERSON>, es gibt mehr!"}, "LV_250": {"name": "Elite", "name_female": "Elite"}, "LV_1000": {"name": "Geh noch höher hinaus!"}, "RibbonAchv": {"description": "Sammle insgesamt {{ribbonAmount}} Bänder."}, "10_RIBBONS": {"name": "Champion der Pokémon-Liga", "name_female": "Champion der Pokémon-Liga"}, "25_RIBBONS": {"name": "Bänder-Sammler", "name_female": "Bänder-Sammlerin"}, "50_RIBBONS": {"name": "<PERSON><PERSON>nder-Experte", "name_female": "<PERSON><PERSON><PERSON>-Expertin"}, "75_RIBBONS": {"name": "<PERSON><PERSON><PERSON>-<PERSON>", "name_female": "<PERSON><PERSON><PERSON>-<PERSON>"}, "100_RIBBONS": {"name": "<PERSON>änder-Meister", "name_female": "Bänder-Meisterin"}, "TRANSFER_MAX_STAT_STAGE": {"name": "Teamwork", "description": "<PERSON><PERSON><PERSON>, während der Anwender mindestens einen Statuswert maximiert hat."}, "MAX_FRIENDSHIP": {"name": "Freundschaftsmaximierung", "description": "Erreiche maximale Freundschaft bei einem Pokémon."}, "MEGA_EVOLVE": {"name": "Megaverwandlung", "description": "Mega-entwickle ein Pokémon."}, "GIGANTAMAX": {"name": "Absolute Einheit", "description": "Gigadynamaximiere ein Pokémon."}, "TERASTALLIZE": {"name": "Typen-Bonus-Enthusiast", "description": "Terakristallisiere ein Pokémon."}, "STELLAR_TERASTALLIZE": {"name": "Der geheime Typ", "description": "Terakristallisiere ein Pokémon zum Typen Stellar."}, "SPLICE": {"name": "Unendliche Fusion", "description": "Kombiniere zwei Pokémon mit einem DNS-Keil."}, "MINI_BLACK_HOLE": {"name": "Ein Loch voller Items", "description": "<PERSON>rl<PERSON>e ein Mini-Schwarzes Loch."}, "CATCH_MYTHICAL": {"name": "Mysteriöses!", "description": "Fange ein mysteriöses Pokémon."}, "CATCH_SUB_LEGENDARY": {"name": "Sub-Legendär", "description": "<PERSON><PERSON> ein sub-legendäres Pokémon."}, "CATCH_LEGENDARY": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON>e ein legendäres Pokémon."}, "SEE_SHINY": {"name": "Schillerndes Licht", "description": "<PERSON>e ein wildes schillerndes <PERSON>."}, "SHINY_PARTY": {"name": "Das ist Hingabe", "name_female": "Das ist Hingabe", "description": "Habe ein Team aus schillernden Pokémon."}, "HATCH_MYTHICAL": {"name": "Mysteriöses E<PERSON>", "description": "Lass ein mysteriöses Pokémon aus einem Ei schlüpfen."}, "HATCH_SUB_LEGENDARY": {"name": "Sub-Legend<PERSON><PERSON>", "description": "Lass ein sub-legendäres Pokémon aus einem Ei schlüpfen."}, "HATCH_LEGENDARY": {"name": "<PERSON><PERSON><PERSON>", "description": "Lass ein legendäres Pokémon aus einem E<PERSON> schlüpfen."}, "HATCH_SHINY": {"name": "Schillerndes Ei", "description": "Lass ein schillerndes Pokémon aus einem E<PERSON> schlüpfen."}, "HIDDEN_ABILITY": {"name": "Geheimes Talent", "description": "<PERSON><PERSON> ein Pokémon mit versteckter Fähigkeit."}, "PERFECT_IVS": {"name": "Echtheitszertifikat", "description": "<PERSON><PERSON><PERSON>e ein Pokémon mit perfekten IS-Werten."}, "CLASSIC_VICTORY": {"name": "Ungeschlagen", "name_female": "Ungeschlagen", "description": "Beende den klassischen Modus erfolgreich."}, "UNEVOLVED_CLASSIC_VICTORY": {"name": "'Bringe dein Kind mit zur Arbeit'-Tag", "description": "Beende den klassischen Modus erfolgreich mit mindestens einem nicht entwickelten Pokémon in deinem Team."}, "MONO_GEN_ONE": {"name": "Der ursprüngliche Rivale", "description": "Beende die 'Mono-Generation: Eins'-Herausforderung."}, "MONO_GEN_TWO": {"name": "Generation 1.5", "description": "Beende die 'Mono-Generation: Z<PERSON>'-Herausforderung."}, "MONO_GEN_THREE": {"name": "<PERSON><PERSON> viel <PERSON>?", "description": "Beende die 'Mono-Generation: Drei'-Herausforderung."}, "MONO_GEN_FOUR": {"name": "Ist SIE wirklich die Stärkste?", "description": "Beende die 'Mono-Generation: Vier'-Herausforderung."}, "MONO_GEN_FIVE": {"name": "Ko<PERSON>tt Original", "description": "Beende die 'Mono-Generation: Fünf'-Herausforderung."}, "MONO_GEN_SIX": {"name": "Fast Königlich", "description": "Beende die 'Mono-Generation: Sechs'-Herausforderung."}, "MONO_GEN_SEVEN": {"name": "Technisch gesehen", "description": "Beende die 'Mono-Generation: Sieben'-Herausforderung."}, "MONO_GEN_EIGHT": {"name": "Die Zeit des Champions", "description": "Beende die 'Mono-Generation: Acht'-Herausforderung."}, "MONO_GEN_NINE": {"name": "Sie hat es dir leicht gemacht …", "description": "Beende die 'Mono-Generation: Neun'-Herausforderung."}, "MonoType": {"description": "Beende die 'Mono-Typ: {{type}}'-Herausforderung."}, "MONO_NORMAL": {"name": "<PERSON><PERSON>"}, "MONO_FIGHTING": {"name": "Ich kann Kung-Fu!"}, "MONO_FLYING": {"name": "Ich glaube, ich kann fliegen!"}, "MONO_POISON": {"name": "<PERSON><PERSON><PERSON>"}, "MONO_GROUND": {"name": "<PERSON><PERSON> dem <PERSON> bleiben"}, "MONO_ROCK": {"name": "So hart wie Rocko"}, "MONO_BUG": {"name": "<PERSON><PERSON> wie ein Bibor!"}, "MONO_GHOST": {"name": "Wer wird angerufen?"}, "MONO_STEEL": {"name": "Stahlharte Entschlossenheit"}, "MONO_FIRE": {"name": "Brennende Leidenschaft"}, "MONO_WATER": {"name": "Wenn es regnet, schüttet es!"}, "MONO_GRASS": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "MONO_ELECTRIC": {"name": "Elektrisierend"}, "MONO_PSYCHIC": {"name": "Übernatürliches Talent"}, "MONO_ICE": {"name": "<PERSON><PERSON>"}, "MONO_DRAGON": {"name": "<PERSON><PERSON><PERSON>, bist du es?"}, "MONO_DARK": {"name": "Es ist nur eine Phase!"}, "MONO_FAIRY": {"name": "Ein ewiges <PERSON>uer!"}, "FRESH_START": {"name": "<PERSON><PERSON>, noch ein<PERSON> von vorn!", "description": "Schließe die 'Neuanfang'-Herausforderung ab"}, "INVERSE_BATTLE": {"name": "<PERSON><PERSON><PERSON><PERSON>, Spieglein an der Wand", "description": "Schließe die 'Umkehrkampf'-Herausforderung ab."}, "FLIP_STATS": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Schließe die 'Statuswert-Flip'-Herausforderung ab."}, "FLIP_INVERSE": {"name": "Umgekehrte Spiegelung", "description": "Schließe die 'Statuswert-Flip'-Herausforderung zusammen mit der 'Umkehrkampf'-Herausforderung ab."}, "BREEDERS_IN_SPACE": {"name": "<PERSON><PERSON>chter im Weltall!", "description": "Besiege einen Pokémon-<PERSON><PERSON><PERSON><PERSON>, eine Pokémon-Züchterin oder die Pokémon-Züchter-Expertin im Stratosphären-Biom."}}