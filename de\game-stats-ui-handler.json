{"stats": "Statistiken", "playTime": "Spielzeit", "totalBattles": "Kämpfe insgesamt", "starters": "Starter", "shinyStarters": "<PERSON><PERSON><PERSON><PERSON>", "speciesSeen": "<PERSON><PERSON><PERSON><PERSON>", "speciesCaught": "Gefangene <PERSON>n", "ribbonsOwned": "Bänder im Besitz", "classicRuns": "Klassik-Modus Versuche", "classicWins": "Klassik-Modus Siege", "dailyRunAttempts": "Täglicher-<PERSON>dus <PERSON>", "dailyRunWins": "Täglicher-Modus Siege", "endlessRuns": "Endlos-<PERSON><PERSON>uche", "highestWaveEndless": "Höchste Welle (Endlos)", "highestMoney": "Max. Geld im Besitz", "highestDamage": "<PERSON><PERSON><PERSON><PERSON>", "highestHPHealed": "Höchste Heilung", "pokemonEncountered": "Getroffene Pokémon", "pokemonDefeated": "Besiegte Pokémon", "pokemonCaught": "Gefangene Pokémon", "eggsHatched": "Ausgebrütete Eier", "subLegendsSeen": "Getroffene Sub-Legenden", "subLegendsCaught": "Gefangene Sub-Legenden", "subLegendsHatched": "Ausgebrütete Sub-Legenden", "legendsSeen": "Getroffene Legenden", "legendsCaught": "Gefang<PERSON>en", "legendsHatched": "Ausgebrütete Legenden", "mythicalsSeen": "<PERSON><PERSON><PERSON><PERSON>", "mythicalsCaught": "<PERSON><PERSON><PERSON><PERSON>", "mythicalsHatched": "Ausgebrütete Mythische", "shiniesSeen": "<PERSON><PERSON><PERSON><PERSON>", "shiniesCaught": "<PERSON><PERSON><PERSON><PERSON>", "shiniesHatched": "Ausgebrütete Schillernde", "pokemonFused": "Pokémon fusioniert", "trainersDefeated": "Besiegte Trainer", "eggsPulled": "Gez<PERSON><PERSON>", "rareEggsPulled": "<PERSON><PERSON><PERSON> Eier Gezogen", "epicEggsPulled": "Epische Eier Gezogen", "legendaryEggsPulled": "<PERSON><PERSON><PERSON>", "manaphyEggsPulled": "<PERSON><PERSON><PERSON>"}