{"battlesWon": "{{count, number}} Kämpfe gewonnen!", "underratedPokemon": "Unterschätztes Pokémon: {{pokemonName}}!", "joinTheDiscord": "<PERSON>tt dem <PERSON> bei!", "infiniteLevels": "Unendliche Level!", "everythingIsStackable": "Alles ist stapelbar*!", "optionalSaveScumming": "Optionales Save Scumming!", "biomes": "38 Biome!", "playWithSpeed": "Spiele mit fünffacher Geschwindigkeit!", "liveBugTesting": "Live-Bug-Tests!", "heavyInfluence": "Starker RoR2-Einfluss!", "pokemonRiskAndPokemonRain": "Pokémon Risk und Pokémon Rain!", "nowWithMoreSalt": "Jetzt mit 33% mehr Salz!", "infiniteFusionAtHome": "Wir haben Infinite Fusionen zu Hause!", "brokenEggMoves": "Übermächtige Ei-Attacken!", "magnificent": "<PERSON><PERSON>!", "doPeopleReadThis": "<PERSON>t das j<PERSON>?", "thatsCrazy": "Das ist verrückt!", "gottaCatchEmAll": "<PERSON><PERSON><PERSON> und schnapp sie dir!", "questionableBalancing": "Fragwürdiges Balancing!", "coolShaders": "Coole Shader!", "aiFree": "Ohne KI!", "suddenDifficultySpikes": "Plötzliche Schwierigkeitsspitzen!", "basedOnAnUnfinishedFlashGame": "Basierend auf einem unfertigen Flash-Spiel!", "moreAddictiveThanIntended": "<PERSON><PERSON><PERSON><PERSON> machender als beabsichtigt!", "mostlyConsistentSeeds": "Meistens konsistente Seeds!", "achievementPointsDontDoAnything": "Errungenschaftspunkte tun nichts!", "nothingBeatsAJellyFilledDonut": "Donuts mit Marmelade,\nes gibt nicht besseres auf der Welt!", "dontTalkAboutThePokemonIncident": "Wir reden nicht über den {{pokemonName}}-Vorfallt!", "alsoTryPokengine": "Versuche auch Pokéngine!", "alsoTryEmeraldRogue": "Versuche auch Emerald Rogue!", "alsoTryRadicalRed": "Versuche auch Radical Red!", "eeveeExpo": "Evoli-Expo!", "checkOutYnoproject": "<PERSON><PERSON><PERSON> dir YNOproject an!", "breedersInSpace": "<PERSON><PERSON>chter im Weltall!", "alsoTryPokemonUnbound": "<PERSON><PERSON><PERSON> mal Pokemon Unbound!", "tryTheJohtoDragonChallenge": "Versuch die Johto Mono-Drache <PERSON>forderung!", "basicReadingAbilityRecommended": "Grundlegendes Leseverständnis empfohlen!", "shoutoutsToTheArtists": "Shoutout an die Künstler!", "gamblingNotEncouraged": "Glücksspiel wird nicht gefördert!", "dontForgetToTakeABreak": "Vergiss nicht eine Pa<PERSON> einzu<PERSON>!", "wEvent": "Geiles Event", "ifItsNotAccurateItsAccurate": "Hat es keine 100%ige Genauigkeit\n trifft es nur in 50% der Fälle.", "everyLossIsProgressMade": "Jede Niederlage ist Fortschritt!", "liveWoChienReaction": "Live Chongjian Reaktion:", "itsAFeatureNotABug": "Es ist ein Feature und kein Fehler!", "theEggsAreNotForEating": "Die Eier sind nicht zum <PERSON> da!", "7.8outOf10TooManyWaterBiomes": "7.8/10, <PERSON><PERSON> viel <PERSON>!", "butNothingHappened": "Nichts geschieht!", "thePowerOfScienceIsAmazing": "Die Wissenschaft kennt keine Grenzen!", "freeToPlay": "Free To Play!", "theresATimeAndPlaceForEverything": "Dies ist weder der rechte Ort\n noch die rechte Zeit dafür!", "nowWithShinierShinies": "Mit schillerenden schillernden Pokémon!", "smilesGoForMiles": "Mit einem <PERSON> werden wir es machen!", "certainlyNotDragonFree": "<PERSON><PERSON><PERSON> nicht ohne Drachen!", "haveANiceDay": "Hab einen schönen Tag!", "redacted": "[ZENSIERT]", "hi": "hi", "transRights": "Transrechte!", "shinyOddsHigherThanYouThink": "Die Chance für schillernde Pokémon\n ist höher als du denkst!", "noFalseTrades": "<PERSON><PERSON> falschen <PERSON>-Täus<PERSON>!", "notForProfit": "Nicht profitorientiert!", "timeForYourDailyRun": "Zeit für deinen Täglichen Durchlauf!", "moreEggsThanADaycare": "<PERSON><PERSON> Eier als der Pokémon-Hort!", "disclaimerHarshSunDoesNotGiveVitaminD": "Warnung: Gleißendes Sonnenlicht liefert KEIN Vitamin D!!", "whoNeedsAMap": "Wer braucht schon eine Ka<PERSON>?", "luxrayIsNotADarkType": "Luxtra ist nicht vom Typ Unlicht!", "selfDestructiveEncounters": "G<PERSON>ner mit Selbstzerstörungstendenzen!", "mostOptionsAreViable": "Die meisten Pokémon sind praktikable Optionen!", "pokerogueMorse": ".--. --- -.- . .-. --- --. ..- .", "smiley": ":)", "beAwareOfPassives": "Achte auf passive Fähigkeiten!", "asSeenOnTheWorldWideWeb": "Wie Im Internet zu sehen!", "vaultinVeluzas": "Mein lieber Scholli!", "tooManyStarters": "<PERSON>u viele Starter!", "checkTheWiki": "Sc<PERSON>u ins Wiki!", "winWithYourFavorites": "Gewinne mit deinen Favoriten!", "alsoTryPokerogueWait": "Probiere auch PokéRogue! Warte mal…", "theWayISeeItKyogreIsSurrounded": "So wie ich das sehe ist Kyogre um<PERSON>t…", "tryOutHoneyGather": "Probiere es mal mit Honigmaul!", "notForTheFaintOfHeart": "Nichts für Zartbesaitete!", "p": "(P)", "flipYourDeviceToEvolveInkay": "Dreh dein Gerät auf den Kopf um Iscalar zu entwickeln!", "inArceusWeTrust": "Wir vertrauen auf Arceus!", "whyDidTheTorchicCrossTheRoad": "Warum überquerte das Flemmli die Straße?", "goodLuck": "Viel Glück!", "fuseWisely": "Fusioniere weise!", "compensation": "Entschädigung?", "prepareForTroubleAndMakeItDouble": "<PERSON>zt gibt es Ärger…und es kommt noch härter!", "anEggForYourTroubles": "Ein Ei für deine Mühen?", "regirock": "ÜN ÜN ÜN", "hereForAGoodTime": "Hier für eine gute Zeit!", "getGoodOrDont": "Werd gut! Oder lass es!", "checkTheSubreddit": "<PERSON><PERSON><PERSON> dir das Subreddit an!", "betterNerfGreninja": "Schwäche Quajutsu lieber ab!", "inCaseOfUpdateClearYourCache": "Update? <PERSON><PERSON> leeren!", "insertTextHere": "*hier Text einfügen*", "endingEndlessNotFound": "ending_endless nicht gefunden", "iLikeMyEggsVouchered": "Ich mag meine E<PERSON> mit Gutscheinen!", "YOU": "DU!", "noAddedSugar": "Ohne <PERSON>er!", "notSponsored": "Nicht gesponsert!", "notRated": "Nicht e<PERSON>uft!", "justOneMoreWaveMom": "Nur noch eine Welle, <PERSON>!", "saltCured": "Gepökelsalzt!", "onlyOnPokerogueNet": "Nur auf pokerogue.net!", "pixelPerfection": "Pixel Perfektion!", "openSource": "Open Source!", "probablyGood": "W<PERSON><PERSON><PERSON>lich ganz gut!", "itsAMonsterHouse": "Ein Monster-Raum!", "dontForgetYourPassword": "Vergiss dein Passwort nicht!", "tripleTripleTripleAxel": "<PERSON><PERSON><PERSON><PERSON>- <PERSON><PERSON><PERSON><PERSON>- Dreifach-<PERSON>!", "questionExclamation": "?!", "clownEncounters": "Clown Begegnungen!", "fullOfBerries": "Voll mit Beeren!", "limitsAreMeantToBeBrokenSometimes": "Regeln sind dazu da,\n geb<PERSON><PERSON> zu werden, manchmal!", "keepItCasual": "Lässig bleiben!", "serversProbablyWorking": "Server funktionieren wahrscheinlich!", "mew": "Mew ist wahrscheinlich nicht unter dem LKW!", "makeItRainAndYourProblemsGoAway": "Goldra<PERSON>ch und deine Probleme sind vergessen!", "customMusicTracks": "Eigene Musiktitel!", "youAreValid": "Du bist wertvoll!", "number591IsLookingOff": "Nummer 591 sieht ein bisschen…", "timeForYourDeliDelivery": "Zeit für deine Botogel-Lieferung!", "goodFirstImpression": "Hoffe wir haben einen guten\n ersten Eindruck hinterlassen!", "iPreferRarerCandies": "Ich bevorzuge Supersonderbonbons!", "pocketRoguelite": "Taschen-Roguelite!", "porygonDidNothingWrong": "Porygon hat nichts falsch gemacht!", "critMattered": "Der Crit hat gemattered!", "pickupNotRequired": "Mit<PERSON>me ist keine Vorraussetzung!", "stayHydrated": "Stay Hydrated!", "alsoTryCobblemon": "Versuche auch Cobblemon!", "alsoTryPokeDoku": "Versuche auch PokeDoku!", "mySleepStyleIsDoesnt": "Meine Schlafpose ist 'Garnicht'.", "makeYourOwnWorldChampDifference": "Sei dein eigener Weltmeister\n und mach den Unterschied!", "yoChampInTheMaking": "Hey, du! Zukünftiger Champ!", "notLiableForDecisionAnxiety": "Keine Haftung für Entscheidungsangst!", "theAirIsTastyHere": "Die Luft hier ist so frisch!", "continue": "Fortsetzen?", "startANewRunToday": "Beginne heute noch einen neuen Durchlauf!", "neverGiveUp": "Gib niemals auf!", "theresAlwaysNextTime": "Es gibt immer ein nächs<PERSON> Mal!", "oneTwoThreeAndPoof": "1, 2 und … … <PERSON><PERSON>wu<PERSON>!", "yourPokemonOnlyGoToLevelOneHundred": "Deine Pokémon können maximal Level 100 erreichen?", "theBattlesWillBeLegendary": "Der Kampf wird legendär werden!", "levelCurveBetterThanJohto": "Bessere Levelkurve als die Johto-Region!", "alsoTryShowering": "Versuche auch *Duschen*!", "wellStillBeHere": "Wir sind noch hier, wenn du zurückkommst!", "weHopeToSeeYouAgain": "Wir hoffen dich wieder zu sehen!", "aHealthyTeamCanMeanGreaterRewards": "Ein gesundes Team kann\n größere Belohnungen bringen!", "aWildPokemonAppeared": "Ein {{pokemon<PERSON><PERSON>}} (wild) erscheint!", "isThisThingOn": "Ist das Ding an?", "needsMoreTesting": "<PERSON><PERSON>ucht mehr <PERSON>!", "whoChecksStatChanges": "Wer überprüft Statusveränderungen?", "whenTwoTrainersEyesMeet": "Wenn sich die Blicke von zwei Trainern treffen, fliegen schon bald die <PERSON>en!", "notOfficiallyOnSteam": "Nicht offiziell auf Steam erhältlich.", "fiftyFifty": "Gewinne den Münzwurf!", "metaNotIncluded": "Meta separat erhältlich!", "bornToBeAWinner": "Als Gewinner geboren!", "onARollout": "<PERSON><PERSON><PERSON>t wie Walzer!", "itsAlwaysNightDeepInTheAbyss": "Im tiefsten Abgrund her<PERSON>t ewige Nacht!", "folksThisIsInsane": "<PERSON><PERSON>, das ist irre!", "newYears": {"happyNewYear": "Frohes neues Jahr!", "andAHappyNewYear": "… Und Frohes Neues!"}, "valentines": {"happyValentines": "<PERSON><PERSON><PERSON>!", "fullOfLove": "Voller Liebe!", "applinForYou": "Wie wärs mit nem Knapfel?", "thePowerOfLoveIsThreeThirtyBST": "Die Kraft der Liebe hat einen Basiswert von 330!", "haveAHeartScale": "<PERSON><PERSON> ne Herzschuppe!", "i<3You": "Ich <3 dich!"}, "aprilFools": {"battlesOne": ">1 Kämpfe gewonnen!", "aprilFools": "April April!", "removedPokemon": "{{pokemon}} wurde entfernt!", "helloKyleAmber": "<PERSON><PERSON>, {{name}}!", "gotcha": "Klasse!", "alsoTryPokerogueTwo": "Versuche auch PokéRogue 2!", "nowWithSameScumCountermeasures": "<PERSON>zt mit Save-Scum-Gegenmaßnahmen!", "neverGonnaGiveYouGoodRolls": "Ich geb dir nie gute Rolls!", "youBumblingBuffoon": "Du Spinatschädel!", "doubleShinyOddsForTrainersOnly": "Schillernde Pokémon jetzt doppelt so häufig…F<PERSON>r Trainer!", "nowWithZMoves": "Jetzt mit Z-Attacken!", "newLightType": "Neuer Licht-Typ!", "removedMegas": "Megas entfernt!", "nerfedYourFavorites": "<PERSON><PERSON> Favoriten wurden schlechter gemacht!", "grrr": "Grr…", "enabledEternatusPassiveGoodLuck": "<PERSON><PERSON><PERSON> hat jetzt seine Passive! Viel Spaß!", "theDarkestDaySoundsLikeAFutureProblem": "Die Finstre Nacht? Da kann sich jemand anderes drum kümmern!", "tmShopWhen": "Wann TM-Shop?", "whoIsFinn": "Wo ist Finn?", "watchOutForShadowPokemon": "Hütet euch vor den Crypto-Pokémon!", "nowWithDarkTypeLuxray": "<PERSON>zt mit Unlicht Luxtra!", "didYouKnow": "Wusstest du schon…?", "onlyOnPokerogueNetAGAIN": "NUR AUF POKEROGUE.NET", "noFreeVouchers": "„Nix da mit Freigutscheinen! Ran an die Arbeit!“", "altffourAchievementPoints": "„Alt + F4 zum Einlösen deiner Erfolgspunkte!“", "areYouPiratingThis": "„Spielst du etwa eine Raubkopie? Wieso?“", "rokePogue": "RokéPogue!", "readMe": "LIES MICH!", "winningNotIncluded": "Gewinnen nicht inkludiert!", "timeForYourSoloUnownRun": "Es ist Zeit für deinen Solo-Lauf mit Icognito!", "nowARealTimeStrategyGame": "Ist jetzt ein Echtzeitstrategiespiel!", "nowWithQuickTimeEncounters": "Neu: Quick-Time-Events bei Begegnungen!", "timeYourInputsForHigherCatchrate": "<PERSON>ücke im richtigen Moment, um die Fangrate zu erhöhen!", "certifiedButtonSimulator": "Jetzt mit echtem Y-Button-Mashing™!", "iHopeYouGetSuckerPunched": "Ich hoffe du erhältst einen Tiefschlag!"}, "halloween": {"happyHalloween": "Frohes Halloween!", "boo": "Boo!", "pumpkabooAbout": "Irrbis überall!", "mayContainSpiders": "Könnte Spuren von Spin<PERSON> enthalten!", "spookyScarySkeledirge": "Gespenstisch gruselige Skelokrok!", "gourgeistUsedTrickOrTreat": "<PERSON><PERSON><PERSON><PERSON><PERSON> setzt Halloween ein!", "letsSnuggleForever": "Herzliche Knuddelkloppe!"}, "winterHoliday": {"happyHolidays": "<PERSON>ohe Weihnachten!", "delibirdSeason": "Botogel-<PERSON>son!", "unaffilicatedWithDelibirdServices": "<PERSON><PERSON> mit Botogel-Diensten!", "diamondsFromTheSky": "Diamantregen vom Himmel!", "holidayStylePikachuNotIncluded": "Festtags-<PERSON><PERSON><PERSON> nicht enthalten!", "delibirdDirectlyToYourHouse": "Botgel-Lieferung direkt nach Hause!", "haveAnIceDay": "Einen frostigen Tag noch!", "spinTheClaydol": "<PERSON>eh das Lepumentas!", "beGoodForGoodnessSake": "Sei brav, um <PERSON><PERSON> willen!", "moomooMilkAndLavaCookies": "Ku<PERSON><PERSON>-<PERSON><PERSON><PERSON> und Lavakekse!", "iNeedAYacheBerry": "Ich brauche eine <PERSON>…", "getJolly": "Werd fröhlich!", "tisTheSeasonToBeSpeSpa": "Es ist die Saison für +Initiative, -Spezial-Angriff!", "deckTheHalls": "<PERSON><PERSON><PERSON><PERSON> das Haus!", "saveScummingGetsYouOnTheNaughtyList": "Save-Scumming bringt dich auf die Unartig-Liste!", "badTrainersGetRolycoly": "Unartige Trainer bekommen Klonkett!"}}