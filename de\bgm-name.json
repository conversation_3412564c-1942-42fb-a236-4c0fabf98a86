{"music": "Musik: ", "missing_entries": "{{name}}", "battle_kanto_champion": "S2W2 Vs. Kanto Champion", "battle_johto_champion": "S2W2 Vs. <PERSON><PERSON>o Champion", "battle_hoenn_champion_g5": "S2W2 <PERSON><PERSON><PERSON> Champion", "battle_hoenn_champion_g6": "ORAS Vs. <PERSON> Champion", "battle_sinnoh_champion": "S2W2 Vs. Champion Cynthia", "battle_champion_alder": "SW Vs. Champion <PERSON><PERSON>", "battle_champion_iris": "S2W2 Vs. Champion <PERSON><PERSON>", "battle_kalos_champion": "XY Vs. Champion <PERSON><PERSON><PERSON>", "battle_champion_kukui": "SM Vs. Alola Champion", "battle_alola_champion": "USUM Vs. Alola Champion", "battle_galar_champion": "SWSH Vs. Champion Delion", "battle_mustard": "SWSH Vs. Ma<PERSON>rich", "battle_champion_geeta": "KAPU Vs. Champion <PERSON><PERSON>", "battle_champion_nemona": "KAPU Vs. Champion Nemila", "battle_champion_kieran": "KAPU Vs. Jo", "battle_hoenn_elite": "ORAS Vs. Hoenn Top Vier", "battle_unova_elite": "SW Vs. Einall Top Vier", "battle_kalos_elite": "XY Vs. Kalos <PERSON> Vier", "battle_alola_elite": "SM Vs. Alola Top Vier", "battle_galar_elite": "SWSH Galar Champ-Cup", "battle_paldea_elite": "KAPU Vs. Paldea Top Vier", "battle_bb_elite": "KAPU Vs. Blaubeer-Top-Vier", "battle_final_encounter": "PMDDX Rayquazas Domäne", "battle_final": "SW Vs. G-Cis", "battle_kanto_gym": "S2W2 Vs. Kanto Arenaleiter", "battle_johto_gym": "S2W2 Vs. <PERSON><PERSON>o Arenaleiter", "battle_hoenn_gym": "S2W2 Vs. <PERSON><PERSON>n <PERSON>iter", "battle_sinnoh_gym": "S2W2 Vs. Sin<PERSON>h Arenaleiter", "battle_unova_gym": "SW Vs. Einall Arenaleiter", "battle_kalos_gym": "XY Vs<PERSON>", "battle_galar_gym": "SWSH Vs. Galar Arenaleiter", "battle_paldea_gym": "KAPU Vs. Paldea Arenaleiter", "battle_legendary_kanto": "XY Vs. <PERSON><PERSON><PERSON>", "battle_legendary_raikou": "HGSS Vs. Raikou", "battle_legendary_entei": "HGSS Vs. Entei", "battle_legendary_suicune": "HGSS Vs. Suicune", "battle_legendary_lugia": "HGSS Vs. Lugia", "battle_legendary_ho_oh": "HGSS Vs. Ho-oh", "battle_legendary_regis_g5": "S2W2 Vs. <PERSON><PERSON><PERSON>", "battle_legendary_regis_g6": "ORAS Vs. <PERSON><PERSON><PERSON>", "battle_legendary_gro_kyo": "ORAS Vs. Groudon & Kyogre", "battle_legendary_rayquaza": "ORAS Vs. <PERSON>", "battle_legendary_deoxys": "ORAS Vs. Deoxys", "battle_legendary_lake_trio": "ORAS Vs. Seen-Trio", "battle_legendary_sinnoh": "ORAS Vs. <PERSON><PERSON><PERSON>", "battle_legendary_dia_pal": "ORAS Vs. Dialga & Palkia", "battle_legendary_origin_forme": "PLA Vs. Urform Dialga & Palkia", "battle_legendary_giratina": "ORAS Vs. Giratina", "battle_legendary_arceus": "HGSS Vs. Arceus", "battle_legendary_unova": "SW Vs. <PERSON><PERSON><PERSON>", "battle_legendary_kyurem": "SW Vs. Kyurem", "battle_legendary_res_zek": "SW Vs. Reshiram & Zekrom", "battle_legendary_xern_yvel": "XY Vs. Xerneas & Yveltal", "battle_legendary_tapu": "SM Vs. Kapu", "battle_legendary_sol_lun": "SM Vs. Solgaleo & Lunala", "battle_legendary_ub": "SM Vs. Ultrabestie", "battle_legendary_dusk_dawn": "USUM Vs. Abendmähne- & Morgenschwingen-Necrozma", "battle_legendary_ultra_nec": "USUM Vs. Ultra-Necrozma", "battle_legendary_zac_zam": "SWSH Vs. Zacian & Zamazenta", "battle_legendary_eternatus_p1": "SWSH Vs. <PERSON>", "battle_legendary_eternatus_p2": "SWSH Vs. Unendynamax-Endynalos", "battle_legendary_glas_spec": "SWSH Vs. Polaross & Phantoross", "battle_legendary_calyrex": "SWSH Vs. Coronospa", "battle_legendary_riders": "SWSH Vs. Schimmelreiter & Rappenreiter Coronospa", "battle_legendary_birds_galar": "SWSH Vs. Legendäre Galar-Vögel", "battle_legendary_ruinous": "KAPU Vs. Schätze des Unheils", "battle_legendary_kor_mir": "KAPU Die Tiefen von Zone Null", "battle_legendary_loyal_three": "KAPU Drei Gefährten", "battle_legendary_ogerpon": "KAPU Vs. Ogerpon", "battle_legendary_terapagos": "KAPU Vs. Terapagos", "battle_legendary_pecharunt": "KAPU Vs. Infamomo", "battle_rival": "SW Vs. Rivale", "battle_rival_2": "SW Vs. N", "battle_rival_3": "SW Vs. N (Finale)", "battle_trainer": "SW Vs. Trainer", "battle_wild": "SW Vs. Wilde Pokémon", "battle_wild_strong": "SW Vs. <PERSON><PERSON>", "end_summit": "PMDDX Gipfel des Himmelturms", "battle_rocket_grunt": "HGSS Vs. Team Rocket Rüpel", "battle_aqua_magma_grunt": "ORAS Vs. Team Aqua & Magma", "battle_galactic_grunt": "SDLP Vs. Team Galaktik Rüpel", "battle_plasma_grunt": "SW Vs. Team Plasma Rüpel", "battle_flare_grunt": "XY Vs. Team F<PERSON><PERSON>", "battle_aether_grunt": "SM Vs. Æther Foundation", "battle_skull_grunt": "SM Vs. Team Skull Rüpel", "battle_macro_grunt": "SWSH Vs. Trainer", "battle_star_grunt": "KAPU Vs. Team Star", "battle_galactic_admin": "SDLP Vs. Team Galactic Commander", "battle_colress": "S2W2 Vs, Achromas", "battle_skull_admin": "SM Vs. Team Skull Vorstand", "battle_oleana": "SWSH Vs. Olivia", "battle_star_admin": "KAPU Vs. Team Star Boss", "battle_rocket_boss": "USUM Vs. Giovanni", "battle_aqua_magma_boss": "ORAS Vs. Team Aqua & Magma Boss", "battle_galactic_boss": "SDLP Vs. Z<PERSON>us", "battle_plasma_boss": "S2W2 Vs. G-Cis", "battle_flare_boss": "XY Vs<PERSON>", "battle_aether_boss": "SM Vs. Samantha", "battle_skull_boss": "SM Vs. <PERSON>", "battle_macro_boss": "SWSH Vs. Rose", "battle_star_boss": "KAPU Vs. Cosima", "abyss": "Firel - Pen<PERSON><PERSON> Chasm", "badlands": "PMD Erkundungsteam Himmel Kargtal", "beach": "PMD Erkundungsteam Himmel Feuchtklippe", "cave": "PMD Erkundungsteam Himmel Himmelsgipfel-Höhle", "construction_site": "PMD Erkundungsteam Himmel Geröllbruch", "desert": "PMD Erkundungsteam Himmel Nordwüste", "dojo": "PMD Erkundungsteam Himmel Knogga-Dojo", "end": "Firel - Darkest Day", "factory": "PMD Erkundungsteam Himmel Verborgene Ruinen", "fairy_cave": "PMD Erkundungsteam Himmel Sternenhöhle", "forest": "Andr06 - Rogue’s Echoes", "grass": "PMD Erkundungsteam Himmel Apfelwald", "graveyard": "<PERSON><PERSON> - <PERSON><PERSON>", "ice_cave": "Firel - -50°C", "island": "PMD Erkundungsteam Himmel Schroffküste", "jungle": "Lmz - Jungle", "laboratory": "Firel - Laboratory", "lake": "Lmz - Lake", "meadow": "PMD Erkundungsteam Himmel Himmelsgipfel-Wald", "metropolis": "Firel - Metropolis", "mountain": "PMD Erkundungsteam Himmel Hornberg", "plains": "Firel - Route 888", "power_plant": "Firel - The Klink", "ruins": "Lmz - Ancient Ruins", "sea": "Andr06 - Marine Mystique", "seabed": "Firel - Seabed", "slum": "Andr06 - <PERSON><PERSON><PERSON>", "snowy_forest": "PMD Erkundungsteam Himmel Himmelsgipfel-Schneefeld", "space": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "swamp": "PMD Erkundungsteam Himmel Ringmeer", "tall_grass": "PMD Erkundungsteam Himmel Nebelwald", "temple": "PMD Erkundungsteam Himmel Ägishöhle", "town": "PMD Erkundungsteam Himmel Zufälliges Dungeon-Theme 3", "volcano": "Firel - Twisturn Volcano", "wasteland": "PMD Erkundungsteam Himmel Verborgenes Hochland", "encounter_ace_trainer": "SW Trainerblicke treffen sich (Ass-Trainer)", "encounter_backpacker": "SW Trainerblicke treffen sich (Backpacker)", "encounter_clerk": "SW Trainerblicke treffen sich (Angestellter)", "encounter_cyclist": "SW Trainerblicke treffen sich (Biker)", "encounter_lass": "SW Trainerblicke treffen sich (Göre)", "encounter_parasol_lady": "SW Trainerblicke treffen sich (Schirmdame)", "encounter_pokefan": "SW Trainerblicke treffen sich (Pokéfan)", "encounter_psychic": "SW Trainerblicke treffen sich (Seher)", "encounter_rich": "SW Trainerblicke treffen sich (Gentleman)", "encounter_rival": "SW Vs. Cheren", "encounter_roughneck": "SW Trainerblicke treffen sich (Raufbold)", "encounter_scientist": "SW Trainerblicke treffen sich (Forscher)", "encounter_twins": "SW Trainerblicke treffen sich (Zwillinge)", "encounter_youngster": "SW Trainerblicke treffen sich (Knirps)", "heal": "SW Pokémon-Heilung", "menu": "PMD Erkundungsteam Himmel Willkommen in der Welt der Pokémon!", "title": "Firel - PokéRogue", "mystery_encounter_weird_dream": "PMD Erkundungsteam Himmel Zeitturmspitze", "mystery_encounter_fun_and_games": "PMD Erkundungsteam Himmel Gildenmeister <PERSON>", "mystery_encounter_gen_5_gts": "SW GTS", "mystery_encounter_gen_6_gts": "XY GTS", "mystery_encounter_delibirdy": "Firel - DeliDelivery!"}