{"atLevel": "Auf Lvl. {{lv}}", "levelUp": "Level Up", "using": "Benötigt {{item}} ({{tier}})", "GREAT": "Super", "ULTRA": "Hyper", "connector": ", ", "gender": "(Nur {{gender}})", "timeOfDay": {"DAY": "Während Tag oder Morgengrauen", "DUSK": "Während Abenddämmerung", "NIGHT": "Während Nacht oder Abenddämmerung", "DAWN": "<PERSON><PERSON><PERSON><PERSON>"}, "move": "Mit erlernter Attacke {{move}}", "moveType": "Mit erlernter Attacke vom Typ {{type}}", "friendship": "<PERSON><PERSON> hoher Zutraulichkeit", "shedinja": "Solange Platz im Team ist", "partyType": "Sol<PERSON><PERSON> ein Pokémon vom Typ {{type}} im Team ist", "caught": "Wenn {{species}} gef<PERSON>en wurde", "weather": "<PERSON><PERSON> bestim<PERSON>tem <PERSON>ter", "treasure": "Mit genügend gesammelten Schätzen", "moveUseCount": "Nach {{count}} <PERSON><PERSON><PERSON><PERSON> <PERSON> {{move}}", "nature": "Mit einem bestimmtem Wesen", "biome": "In einem bestimmten Biome", "heldItem": {"DEEP_SEA_TOOTH": "<PERSON><PERSON> getragen", "DEEP_SEA_SCALE": "<PERSON>n <PERSON>platte getragen"}, "Forms": {"ability": "Durch seine Fähigkeit", "item": "{{item}} aktivieren", "deactivateItem": "{{item}} deaktivieren", "timeOfDay": "An einer bestimmten Tageszeit", "enter": "<PERSON><PERSON>", "leave": "<PERSON><PERSON> des Kampfs", "statusEffect": "<PERSON><PERSON> von e<PERSON>m bestimmten Statuseffekt betroffen", "moveLearned": "Durch das erlenen von {{move}}", "moveForgotten": "Durch das Vergessen von {{move}}", "move": "<PERSON>rch den Einsatz von {{move}}", "tera": "Durch Terakristallisierung", "teraLapse": "Nach Ende der Terrakristallisierung", "weather": "<PERSON><PERSON>", "weatherRevert": "Bei Wetteränderung", "preMove": "Vor dem Einsatz bestimmter Attacken", "postMove": "Nach dem Einsatz bestimmter Attacken"}}