{"intro": "Es ist die Safari-Zone!", "title": "Die Safari-Zone", "description": "Es gibt alle Arten von seltenen und besonderen Pokémon, die hier gefunden werden können!\nWenn du dich entsche<PERSON>t, ein<PERSON><PERSON><PERSON>, hast du kannst du in den nächsten@[TOOLTIP_TITLE]{ {{numEncounters}} Wellen} versuchen, besondere Pokémon zu fangen.\nAber sei gewarnt, diese Pokémon können fliehen, bevor du sie fangen kannst!", "query": "Willst du eintreten?", "option": {"1": {"label": "Eintreten", "tooltip": "(-) Zahle {{option1Money, money}}\n@[SUMMARY_GREEN]{(?) Safari Zone}", "selected": "<PERSON><PERSON>, dein Glück herauszufordern!"}, "2": {"label": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "(-) <PERSON><PERSON>", "selected": "Du gehst deines Weges, mit einem leichten Gefühl der Reue."}}, "safari": {"1": {"label": "Pokéball werfen", "tooltip": "(+) Werfe einen Pokéball", "selected": "Du wirfst einen Pokéball!"}, "2": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "(+) <PERSON>rh<PERSON>ht die Fangrate\n(-) <PERSON>rhöht die Fluchtchance", "selected": "Du wirfst einen Köder!"}, "3": {"label": "<PERSON><PERSON>", "tooltip": "(+) <PERSON>ermindert die Fluchtchance\n(-) <PERSON>, die Fangrate zu verringern", "selected": "Du wirst ein wenig <PERSON>!"}, "4": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "(?) <PERSON><PERSON><PERSON> vor diesem Pokémon"}, "watching": "{{pokemon<PERSON>ame}} beobachtet alles aufmerksam!", "eating": "{{pokemon<PERSON>ame}} frisst!", "busy_eating": "{{pokemon<PERSON>ame}} konzen<PERSON><PERSON> sich aufs Futter!", "angry": "{{pokemon<PERSON>ame}} ist wütend!", "beside_itself_angry": "{{pokemon<PERSON>ame}} ist außer sich vor Wut!", "remaining_count": "{{remainingCount}} Pokémon übrig!"}, "outro": "Das war ein spannendes Abenteuer in der Safari-Zone!"}