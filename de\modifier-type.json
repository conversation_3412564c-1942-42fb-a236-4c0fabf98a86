{"ModifierType": {"AddPokeballModifierType": {"name": "{{modifierCount}}x {{pokeball<PERSON>ame}}", "description": "Erhalte {{pokeballName}} x{{modifierCount}}. (Inventar: {{pokeballAmount}}) \nFangrate: {{catchRate}}"}, "AddVoucherModifierType": {"name": "{{modifierCount}}x {{voucherTypeName}}", "description": "Erhalte {{voucherTypeName}} x{{modifierCount}}."}, "PokemonHeldItemModifierType": {"extra": {"inoperable": "{{pokemon<PERSON>ame}} kann dieses\nItem nicht nehmen!", "tooMany": "{{pokemon<PERSON>ame}} hat zu viele\nvon diesem Item!"}}, "PokemonHpRestoreModifierType": {"description": "Füllt {{restorePoints}} KP oder {{restorePercent}}% der KP für ein Pokémon auf. Je nachdem, welcher Wert höher ist.", "extra": {"fully": "Füllt die KP eines Pokémon wieder vollständig auf.", "fullyWithStatus": "Füllt die KP eines Pokémon wieder vollständig auf und behebt alle Statusprobleme."}}, "PokemonReviveModifierType": {"description": "Belebt ein kampunfähiges Pokémon wieder und stellt {{restorePercent}}% KP wieder her."}, "PokemonStatusHealModifierType": {"description": "Behebt alle Statusprobleme eines Pokémon."}, "PokemonPpRestoreModifierType": {"description": "Füllt {{restorePoints}} AP der ausgewählten Attacke eines Pokémon auf.", "extra": {"fully": "Füllt alle AP der ausgewählten Attacke eines Pokémon auf."}}, "PokemonAllMovePpRestoreModifierType": {"description": "Stellt {{restorePoints}} AP für alle Attacken eines Pokémon auf.", "extra": {"fully": "Füllt alle AP für alle Attacken eines Pokémon auf."}}, "PokemonPpUpModifierType": {"description": "<PERSON><PERSON><PERSON><PERSON>ht die maximale Anzahl der AP der ausgewählten Attacke um {{upPoints}} für jede 5 maximale AP (maximal 3)."}, "PokemonNatureChangeModifierType": {"name": "{{natureName}} <PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> das Wesen zu {{natureName}}. Schaltet dieses Wesen permanent für diesen Starter frei."}, "DoubleBattleChanceBoosterModifierType": {"description": "Vervierfacht die Chance, dass ein Kampf ein Doppelkampf wird, für bis zu {{battleCount}} Kämpfe."}, "TempStatStageBoosterModifierType": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{stat}} aller Teammitglieder um {{amount}} für bis zu 5 Kämpfe.", "extra": {"stage": "eine <PERSON>e", "percentage": "30%"}}, "AttackTypeBoosterModifierType": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke aller {{moveType}}-<PERSON><PERSON> eines Pokémon um 20%."}, "PokemonLevelIncrementModifierType": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> das Level eines Pokémon um {{levels}}."}, "AllPokemonLevelIncrementModifierType": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> das Level aller Teammitglieder um {{levels}}."}, "BaseStatBoosterModifierType": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> den {{stat}} Basiswert des Trägers um 10%. Das Stapellimit erhöht sich, je höher dein IS-Wert ist."}, "PokemonBaseStatFlatModifierType": {"name": "Spezialität", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> den {{stats}}-Wert des Trägers um {{statValue}}. Nach einem komischen Traum gefunden."}, "AllPokemonFullHpRestoreModifierType": {"description": "<PERSON><PERSON><PERSON> 100% der KP aller Pokémon her."}, "AllPokemonFullReviveModifierType": {"description": "Belebt alle kampunfähigen Pokémon wieder und stellt ihre KP vollständig wieder her."}, "MoneyRewardModifierType": {"description": "G<PERSON><PERSON><PERSON><PERSON> einen {{moneyMultiplier}} <PERSON>eldbet<PERSON> von (₽{{moneyAmount}}).", "extra": {"small": "kleinen", "moderate": "moderaten", "large": "großen"}}, "ExpBoosterModifierType": {"description": "Erh<PERSON>ht die erhaltenen Erfahrungspunkte um {{boostPercent}}%."}, "PokemonExpBoosterModifierType": {"description": "Er<PERSON><PERSON>ht die Menge der erhaltenen Erfahrungspunkte für den Träger um {{boostPercent}}%."}, "PokemonFriendshipBoosterModifierType": {"description": "Erhöht den Freundschaftszuwachs um 50%."}, "PokemonMoveAccuracyBoosterModifierType": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Genauigkeit der Angriffe um {{accuracyAmount}}."}, "PokemonMultiHitModifierType": {"description": "Wandelt 25 % des Schadens von den Attacken des Trägers in einen zusätzlichen Treffer um."}, "TmModifierType": {"name": "TM{{moveId}} - {{moveName}}", "description": "Bringt einem Pokémon {{moveName}} bei."}, "TmModifierTypeWithInfo": {"name": "TM{{moveId}} - {{moveName}}", "description": "Bringt einem Pokémon {{moveName}} bei\n(Halte C oder Shift für mehr Infos)."}, "EvolutionItemModifierType": {"description": "Erlaubt es bestimmten Pokémon sich zu entwickeln."}, "FormChangeItemModifierType": {"description": "Erlaubt es bestimmten Pokémon ihre Form zu ändern."}, "FusePokemonModifierType": {"description": "Fusioniert zwei Pokémon (überträgt die Fähigkeit, teilt Basiswerte und Typ auf, gemeinsamer Attackenpool)."}, "TerastallizeModifierType": {"name": "Tera-Stück ({{teraType}})", "description": "<PERSON><PERSON><PERSON> den Tera-Typ des Pokémon zu {{teraType}}."}, "ContactHeldItemTransferChanceModifierType": {"description": "<PERSON><PERSON> besteht eine {{chancePercent}}%ige Chance, dass das getragene Item des Gegners gestohlen wird."}, "TurnHeldItemTransferModifierType": {"description": "Jede Runde erhält der Träger ein getragenes Item des Gegners."}, "EnemyAttackStatusEffectChanceModifierType": {"description": "<PERSON><PERSON><PERSON> eine {{chancePercent}}%ige <PERSON> hinzu, {{statusEffect}} zu veru<PERSON>chen."}, "EnemyEndureChanceModifierType": {"description": "G<PERSON><PERSON> den Träger eine {{chancePercent}}%ige <PERSON>, einen Angriff zu überleben."}, "RARE_CANDY": {"name": "Sonderbonbon"}, "RARER_CANDY": {"name": "Supersondererbonbon"}, "MEGA_BRACELET": {"name": "Mega-Armband", "description": "Mega-Steine werden verfügbar."}, "DYNAMAX_BAND": {"name": "Dynamax-Band", "description": "Dyna-Pilze werden verfügbar."}, "TERA_ORB": {"name": "Terakristall-Orb", "description": "Erlaubt es deinem Pokémon die Terakristallisierung durchzuführen. Läd langsam auf. Lässt Tera-Stück erscheinen."}, "MAP": {"name": "<PERSON><PERSON>", "description": "Ermöglicht es dir, an einer Kreuzung dein Ziel zu wählen."}, "POTION": {"name": "<PERSON>rank"}, "SUPER_POTION": {"name": "Supertrank"}, "HYPER_POTION": {"name": "Hypertrank"}, "MAX_POTION": {"name": "Top-Trank"}, "FULL_RESTORE": {"name": "Top-Genesung"}, "REVIVE": {"name": "<PERSON><PERSON><PERSON>"}, "MAX_REVIVE": {"name": "Top-<PERSON><PERSON><PERSON>"}, "FULL_HEAL": {"name": "Hyperheiler"}, "SACRED_ASH": {"name": "Zauberasche"}, "REVIVER_SEED": {"name": "Belebersamen", "description": "Bel<PERSON><PERSON> den Träger mit der Hälfte seiner KP wieder, sollte er durch einen direkten Treffer kampfunfähig werden."}, "WHITE_HERB": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Ein Item zum Tragen. Es hebt einmalig jede negative Statuswertveränderung auf."}, "ETHER": {"name": "<PERSON><PERSON>"}, "MAX_ETHER": {"name": "Top-Äther"}, "ELIXIR": {"name": "<PERSON><PERSON><PERSON>"}, "MAX_ELIXIR": {"name": "Top-Elixir"}, "PP_UP": {"name": "AP-Plus"}, "PP_MAX": {"name": "AP-Top"}, "LURE": {"name": "Lockparfüm"}, "SUPER_LURE": {"name": "Super-Lockparfüm"}, "MAX_LURE": {"name": "Top-Lockparfüm"}, "MEMORY_MUSHROOM": {"name": "Erinnerungspilz", "description": "Lässt ein Pokémon eine vergessene Attacke wiedererlernen."}, "EXP_SHARE": {"name": "EP-Teiler", "description": "Pokémon, die nicht am Kampf teilgenommen haben, bekommen 20% der Erfahrungspunkte eines Kampfteilnehmers."}, "EXP_BALANCE": {"name": "EP-Ausgleicher", "description": "Gewichtet die in Kämpfen erhaltenen Erfahrungspunkte auf niedrigstufigere Gruppenmitglieder."}, "OVAL_CHARM": {"name": "<PERSON><PERSON>", "description": "Wenn mehrere Pokémon am Kampf teilnehmen, erh<PERSON><PERSON>t jeder von <PERSON> 10% extra Erfahrungspunkte."}, "EXP_CHARM": {"name": "EP-Pin"}, "SUPER_EXP_CHARM": {"name": "Super-EP-Pin"}, "GOLDEN_EXP_CHARM": {"name": "Goldener EP-Pin"}, "LUCKY_EGG": {"name": "Glücks-Ei"}, "GOLDEN_EGG": {"name": "Goldenes Ei"}, "SOOTHE_BELL": {"name": "Sanftglocke"}, "SCOPE_LENS": {"name": "Scope-<PERSON><PERSON>", "description": "Ein Item zum Tragen. Es erhöht die Volltrefferquote."}, "DIRE_HIT": {"name": "X-Volltreffer", "extra": {"raises": "Volltrefferquote"}}, "LEEK": {"name": "Lauchstange", "description": "<PERSON>em, das von Porenta getragen werden kann. Diese lange Lauchstange erhöht die Volltrefferquote stark."}, "EVIOLITE": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Ein mysteriöser Klumpen, der die Vert. u. Spez.-V<PERSON>. von <PERSON>h<PERSON>, die sich noch entwickeln können."}, "SOUL_DEW": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> den Einfluss des Wesens eines Pokemon auf seine Werte um 10% (additiv)."}, "NUGGET": {"name": "Nugget"}, "BIG_NUGGET": {"name": "R<PERSON>ennugget"}, "RELIC_GOLD": {"name": "<PERSON><PERSON>"}, "AMULET_COIN": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Erhöht das Preisgeld um 20%."}, "GOLDEN_PUNCH": {"name": "Goldschlag", "description": "Gewährt Geld in Höhe von 50% des zugefügten Schadens."}, "COIN_CASE": {"name": "Münzkorb", "description": "Erhalte nach jedem 10ten Kampf 10% Zinsen auf dein Geld."}, "LOCK_CAPSULE": {"name": "Tresorkapsel", "description": "Erlaubt es die Seltenheitsstufe der Items festzusetzen wenn diese neu gerollt werden."}, "GRIP_CLAW": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "WIDE_LENS": {"name": "Großlinse"}, "MULTI_LENS": {"name": "Me<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "HEALING_CHARM": {"name": "Heilungspin", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Effektivität von Heilungsattacken sowie Heilitems um 10% (Beleber ausgenommen)."}, "CANDY_JAR": {"name": "<PERSON>bong<PERSON>", "description": "Erh<PERSON>ht die Anzahl der Level die ein Sonderbonbon erhöht um 1."}, "BERRY_POUCH": {"name": "Beerentüte", "description": "<PERSON><PERSON><PERSON> eine 30% Chance hinzu, dass Beeren nicht verbraucht werden."}, "FOCUS_BAND": {"name": "Fokusband", "description": "<PERSON><PERSON><PERSON> eine 10% <PERSON> hinzu, dass Angriffe die zur Kampfunfähigkeit führen mit 1 KP überlebt werden."}, "QUICK_CLAW": {"name": "Flinkklaue", "description": "<PERSON><PERSON><PERSON> eine 10% Change hinzu als erster anzugreifen. (Nach Prioritätsangriffen)."}, "KINGS_ROCK": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> eine 10% Chance hinzu, dass der Gegner nach einem Angriff zurückschreckt."}, "LEFTOVERS": {"name": "Überreste", "description": "Heilt 1/16 der maximalen KP eines Pokémon pro Runde."}, "SHELL_BELL": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Heilt den Anwender um 1/8 des von ihm zugefügten Schadens."}, "TOXIC_ORB": {"name": "Toxik-Orb", "description": "Dieser bizarre Orb vergiftet seinen Träger im Kampf schwer."}, "FLAME_ORB": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Dieser bizarre Orb fügt seinem Träger im Kampf Verbrennungen zu."}, "MYSTICAL_ROCK": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>er<PERSON>ä<PERSON><PERSON> die Dauer von <PERSON>, dass durch Attacken oder Fähigkeiten des Trägers ausgelöst wurde, um 2 Runden pro Stapel."}, "EVOLUTION_TRACKER_GIMMIGHOUL": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Dieses Pokémon liebt Schätze! <PERSON>mle weiter Schätze und vielleicht passiert etwas!"}, "EVOLUTION_TRACKER_PRIMEAPE": {"name": "Zornesfaust", "description": "<PERSON><PERSON><PERSON> entwick<PERSON>t sich, wenn seine Wut die Grenzen seiner physischen Form übersteigt!\nSteigere seine <PERSON>, indem du die Attacke Zornfaust einsetzt!"}, "EVOLUTION_TRACKER_STANTLER": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Damhir<PERSON> entwickelt sich, um sich an raue Umgebungen anzupassen.\nStärke seine Verteidigung, indem du die Attacke Barrierenstoß einsetzt!"}, "BATON": {"name": "Stab", "description": "Ermöglicht das Weitergeben von Effekten beim Wechseln von Pokémon, wodurch auch Fallen umgangen werden."}, "SHINY_CHARM": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Chance deutlich, dass ein wildes Pokémon ein schillernd ist."}, "ABILITY_CHARM": {"name": "Fähigkeitspin", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Chance deutlich, dass ein wildes Pokémon eine versteckte Fähigkeit hat."}, "CATCHING_CHARM": {"name": "Superfangpin", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Wahrscheinlichkeit einen Superfang hinzulegen."}, "IV_SCANNER": {"name": "IS-Scanner", "description": "Erlaubt es die IS-Werte von wilden Pokémon zu scannen.\n(2 IS-Werte pro Staplung. Die besten IS-Werte zuerst)."}, "DNA_SPLICERS": {"name": "DNS-Keil"}, "MINI_BLACK_HOLE": {"name": "Mini schwarzes Loch"}, "GOLDEN_POKEBALL": {"name": "Goldener Pokéball", "description": "<PERSON>ügt eine zusätzliche Item-Auswahlmöglichkeit nach jedem Kampf hinzu."}, "ENEMY_DAMAGE_BOOSTER": {"name": "Schadensmarke", "description": "Erhöht den Schaden um 5%."}, "ENEMY_DAMAGE_REDUCTION": {"name": "Sc<PERSON>tzmark<PERSON>", "description": "Verringert den erhaltenen Schaden um 2,5%."}, "ENEMY_HEAL": {"name": "Wiederherstellungsmarke", "description": "Heilt 2% der maximalen KP pro Runde."}, "ENEMY_ATTACK_POISON_CHANCE": {"name": "Giftmarke"}, "ENEMY_ATTACK_PARALYZE_CHANCE": {"name": "Lähmungsmarke"}, "ENEMY_ATTACK_BURN_CHANCE": {"name": "Brandmarke"}, "ENEMY_STATUS_EFFECT_HEAL_CHANCE": {"name": "Vollheilungsmarke", "description": "<PERSON><PERSON><PERSON> eine 2,5%ige <PERSON>, jede Runde einen Statuszustand zu heilen."}, "ENEMY_ENDURE_CHANCE": {"name": "Ausdauer-Marke"}, "ENEMY_FUSED_CHANCE": {"name": "Fusionsmarke", "description": "Fügt eine 1%ige <PERSON> hinzu, dass ein wildes Pokémon eine Fusion ist."}, "MYSTERY_ENCOUNTER_SHUCKLE_JUICE_GOOD": {"name": "Süßer Pottrottsaft", "description": "<PERSON>rhöht alle Basiswerte des Trägers. Du wurdest von <PERSON>ttrott gesegnet."}, "MYSTERY_ENCOUNTER_SHUCKLE_JUICE_BAD": {"name": "Ranziger Pottrottsaft", "description": "Verringert alle Basiswerte des Trägers. Du wurdest von Pottrott verflucht."}, "MYSTERY_ENCOUNTER_BLACK_SLUDGE": {"name": "Giftschleim", "description": "Der Geruch ist so stark, dass die Geschäfte ihre Items nur zu einem stark erhöhten Preis verkaufen."}, "MYSTERY_ENCOUNTER_MACHO_BRACE": {"name": "Machoschiene", "description": "Das Besiegen eines Pokémon gewährt dem Besitzer einen Machoschiene-Stapel. Jeder Stapel steigert die Werte leicht, mit einem zusätzlichen Bonus bei maximalen Stapeln."}, "MYSTERY_ENCOUNTER_OLD_GATEAU": {"name": "Spezialität", "description": "<PERSON>rhöht einige der Werte des Trägers."}, "MYSTERY_ENCOUNTER_GOLDEN_BUG_NET": {"name": "Goldenes Käfersammlernetz", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Chance, dass der Besitzer mehr Pokémon vom Typ Käfer findet. Hat ein seltsames Gewicht."}}, "SpeciesBoosterItem": {"LIGHT_BALL": {"name": "Kugelblitz", "description": "<PERSON>, das von Pikachu getragen werden kann. Es erhöht den Angriff und den Spezial-Angriff."}, "THICK_CLUB": {"name": "Kampfknochen", "description": "<PERSON>em, das von Tragosso oder Knogga getragen werden kann. Dieser harte Knochen erhöht den Angriff."}, "METAL_POWDER": {"name": "Metallstaub", "description": "<PERSON>, das von Ditto getragen werden kann. Fein und doch hart, erhöht dieses sonderbare Pulver die Verteidigung."}, "QUICK_POWDER": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>em, das Ditto zum Tragen gegeben werden kann. Fein und doch hart, erhöht dieses sonderbare Pulver die Initiative."}, "DEEP_SEA_SCALE": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> schimmert pink und erhöht die Spezial-Verteidigung von <PERSON>."}, "DEEP_SEA_TOOTH": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>s schimmert silbern und erhöht den Spezial-<PERSON><PERSON> von <PERSON>."}}, "TempStatStageBoosterItem": {"x_attack": "X-Angriff", "x_defense": "X-Verteidigung", "x_sp_atk": "X-Sp.-Ang.", "x_sp_def": "X-Sp.-Vert.", "x_speed": "X-Tempo", "x_accuracy": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "AttackTypeBoosterItem": {"silk_scarf": "Seidenschal", "black_belt": "Schwarz<PERSON>rt", "sharp_beak": "Spitzer Schnabel", "poison_barb": "Giftstich", "soft_sand": "<PERSON><PERSON><PERSON><PERSON>", "hard_stone": "<PERSON><PERSON><PERSON>", "silver_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spell_tag": "<PERSON><PERSON><PERSON>", "metal_coat": "Metallmantel", "charcoal": "Holzkohle", "mystic_water": "Zauberwasser", "miracle_seed": "<PERSON><PERSON><PERSON><PERSON>", "magnet": "<PERSON><PERSON><PERSON>", "twisted_spoon": "Krümmlöffel", "never_melt_ice": "<PERSON><PERSON><PERSON>", "dragon_fang": "<PERSON><PERSON><PERSON><PERSON>", "black_glasses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fairy_feather": "Feendaune"}, "BaseStatBoosterItem": {"hp_up": "KP-Plus", "protein": "<PERSON><PERSON>", "iron": "Eisen", "calcium": "Kalzium", "zinc": "Zink", "carbos": "Carbon"}, "EvolutionItem": {"NONE": "<PERSON><PERSON>", "LINKING_CORD": "<PERSON><PERSON><PERSON>", "SUN_STONE": "Sonnenstein", "MOON_STONE": "<PERSON><PERSON><PERSON>", "LEAF_STONE": "<PERSON><PERSON><PERSON>", "FIRE_STONE": "<PERSON><PERSON><PERSON>", "WATER_STONE": "<PERSON>serstein", "THUNDER_STONE": "<PERSON><PERSON><PERSON>", "ICE_STONE": "<PERSON><PERSON><PERSON>", "DUSK_STONE": "<PERSON><PERSON><PERSON>", "DAWN_STONE": "<PERSON><PERSON><PERSON>", "SHINY_STONE": "<PERSON><PERSON><PERSON><PERSON>", "CRACKED_POT": "<PERSON><PERSON><PERSON>", "SWEET_APPLE": "<PERSON><PERSON><PERSON><PERSON>", "TART_APPLE": "Saurer Apfel", "STRAWBERRY_SWEET": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "UNREMARKABLE_TEACUP": "<PERSON> Teeschale", "UPGRADE": "Upgrade", "DUBIOUS_DISC": "Dubiosdisc", "DRAGON_SCALE": "Drachenschuppe", "PRISM_SCALE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RAZOR_CLAW": "Scharfklaue", "RAZOR_FANG": "Sc<PERSON>zahn", "REAPER_CLOTH": "Düsterumhang", "ELECTIRIZER": "<PERSON><PERSON><PERSON><PERSON>", "MAGMARIZER": "<PERSON><PERSON><PERSON><PERSON>", "PROTECTOR": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SACHET": "Duftbeutel", "WHIPPED_DREAM": "Sa<PERSON>ehä<PERSON>chen", "LEADERS_CREST": "Anführersymbol", "SUN_FLUTE": "Sonnenflöte", "MOON_FLUTE": "Mondflöte", "CHIPPED_POT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BLACK_AUGURITE": "Schwarzaugit", "GALARICA_CUFF": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GALARICA_WREATH": "Galarnuss-Kranz", "PEAT_BLOCK": "Torf<PERSON>", "AUSPICIOUS_ARMOR": "Glorienrüstung", "MALICIOUS_ARMOR": "Fluchrüstung", "MASTERPIECE_TEACUP": "<PERSON><PERSON>", "METAL_ALLOY": "Legierungsmetall", "SCROLL_OF_DARKNESS": "Unlicht-Schriftrolle", "SCROLL_OF_WATERS": "Wasser-Schriftrolle", "SYRUPY_APPLE": "Saftiger Apfel"}, "FormChangeItem": {"NONE": "<PERSON><PERSON>", "ABOMASITE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ABSOLITE": "Absolnit", "AERODACTYLITE": "Aerodactylonit", "AGGRONITE": "Stollossnit", "ALAKAZITE": "Simsalanit", "ALTARIANITE": "Altarianit", "AMPHAROSITE": "Ampharosnit", "AUDINITE": "Ohrdochnit", "BANETTITE": "<PERSON><PERSON><PERSON><PERSON>", "BEEDRILLITE": "Bibornit", "BLASTOISINITE": "Turtoknit", "BLAZIKENITE": "Lohgocknit", "CAMERUPTITE": "Cameruptnit", "CHARIZARDITE_X": "Gluraknit X", "CHARIZARDITE_Y": "Gluraknit Y", "DIANCITE": "Diancienit", "GALLADITE": "Galagladinit", "GARCHOMPITE": "Knakracknit", "GARDEVOIRITE": "Guardevoirnit", "GENGARITE": "Gengarnit ", "GLALITITE": "Firnontornit", "GYARADOSITE": "Garadosnit", "HERACRONITE": "Skarabornit", "HOUNDOOMINITE": "Hundemonit ", "KANGASKHANITE": "Kangamanit", "LATIASITE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LATIOSITE": "La<PERSON><PERSON><PERSON><PERSON>", "LOPUNNITE": "Schlapornit", "LUCARIONITE": "Lucarionit", "MANECTITE": "Voltensonit", "MAWILITE": "Flunkifernit", "MEDICHAMITE": "Meditalisnit", "METAGROSSITE": "Metagrossnit", "MEWTWONITE_X": "Mewtunit X", "MEWTWONITE_Y": "Mewtunit Y", "PIDGEOTITE": "Taubossnit", "PINSIRITE": "Pinsirnit", "RAYQUAZITE": "Rayquazanit", "SABLENITE": "Zobirisnit", "SALAMENCITE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SCEPTILITE": "Gewaldronit", "SCIZORITE": "Scheroxnit", "SHARPEDONITE": "To<PERSON>donit", "SLOWBRONITE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STEELIXITE": "Stahlosnit", "SWAMPERTITE": "Sumpexnit", "TYRANITARITE": "Despotarnit", "VENUSAURITE": "Bisaflornit", "BLUE_ORB": "<PERSON><PERSON><PERSON>", "RED_ORB": "<PERSON><PERSON>", "SHARP_METEORITE": "<PERSON><PERSON>er Meteorit", "HARD_METEORITE": "<PERSON><PERSON> Meteorit", "SMOOTH_METEORITE": "<PERSON><PERSON> Meteorit", "ADAMANT_CRYSTAL": "Adamantkristall", "LUSTROUS_GLOBE": "Weißkristall", "GRISEOUS_CORE": "Platinumkristall", "REVEAL_GLASS": "Wahrspiegel", "GRACIDEA": "Gracidea", "MAX_MUSHROOMS": "Dyna-Pilz", "DARK_STONE": "Dunkelstein", "LIGHT_STONE": "<PERSON><PERSON>stein", "PRISON_BOTTLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "N_LUNARIZER": "<PERSON><PERSON><PERSON><PERSON>", "N_SOLARIZER": "Necrosol", "RUSTED_SWORD": "Rostiges Schwert", "RUSTED_SHIELD": "Rostiges Schild", "ICY_REINS_OF_UNITY": "Eisige Zügel des Bundes", "SHADOW_REINS_OF_UNITY": "Schattige Zügel des Bundes", "WELLSPRING_MASK": "Brunnenmaske", "HEARTHFLAME_MASK": "Ofenmaske", "CORNERSTONE_MASK": "Fundamentmaske", "SHOCK_DRIVE": "Blitzmodul", "BURN_DRIVE": "Flammenmodul", "CHILL_DRIVE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DOUSE_DRIVE": "Aquamodul", "ULTRANECROZIUM_Z": "Ultranecrozium Z", "FIST_PLATE": "Fausttafel", "SKY_PLATE": "Wolkentafel", "TOXIC_PLATE": "Gifttafel", "EARTH_PLATE": "Erdtafel", "STONE_PLATE": "Steintafel", "INSECT_PLATE": "Käfertafel", "SPOOKY_PLATE": "Spuktafel", "IRON_PLATE": "Eisentafel", "FLAME_PLATE": "Feuertafel", "SPLASH_PLATE": "Wassertafel", "MEADOW_PLATE": "Wiesentafel", "ZAP_PLATE": "Blitztafel", "MIND_PLATE": "<PERSON>rn<PERSON><PERSON><PERSON>", "ICICLE_PLATE": "Frosttafel", "DRACO_PLATE": "Dracotafel", "DREAD_PLATE": "Furchttafel", "PIXIE_PLATE": "Feentafel", "BLANK_PLATE": "Neutraltafel", "LEGEND_PLATE": "Legendentafel", "FIGHTING_MEMORY": "Kampf-Disc", "FLYING_MEMORY": "Flug-Disc", "POISON_MEMORY": "Gift-Disc", "GROUND_MEMORY": "Boden-Disc", "ROCK_MEMORY": "Gesteins-Disc", "BUG_MEMORY": "Käfer-Disc", "GHOST_MEMORY": "Geister-Disc", "STEEL_MEMORY": "Stahl-Disc", "FIRE_MEMORY": "<PERSON>uer-Disc", "WATER_MEMORY": "Wasser-Disc", "GRASS_MEMORY": "Pflanzen-Disc", "ELECTRIC_MEMORY": "Elektro-Disc", "PSYCHIC_MEMORY": "Psycho-Disc", "ICE_MEMORY": "Eis-Disc", "DRAGON_MEMORY": "Drachen-Disc", "DARK_MEMORY": "Unlicht-Disc", "FAIRY_MEMORY": "Feen-Disc", "NORMAL_MEMORY": "Normal-Disc"}}