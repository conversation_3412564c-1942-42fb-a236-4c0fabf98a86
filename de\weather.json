{"sunnyStartMessage": "Die Sonnenlicht wird stärker!", "sunnyLapseMessage": "Die Sonnenlicht ist stark.", "sunnyClearMessage": "Die Sonnenlicht verliert wieder an Intensität.", "rainStartMessage": "Es fängt an zu regnen!", "rainLapseMessage": "<PERSON><PERSON> regnet weiter.", "rainClearMessage": "Der Regen lässt nach.", "sandstormStartMessage": "Ein Sandsturm kommt auf!", "sandstormLapseMessage": "<PERSON> tobt.", "sandstormClearMessage": "Der Sandsturm legt sich.", "sandstormDamageMessage": " <PERSON> Sandsturm fügt {{pokemonNameWithAffix}} <PERSON><PERSON><PERSON> zu!", "hailStartMessage": "Es fängt an zu hageln!", "hailLapseMessage": "Der Hagelsturm tobt.", "hailClearMessage": "Der Hagelsturm legt sich.", "hailDamageMessage": "{{pokemonNameWithAffix}} wird von <PERSON> getroffen!", "snowStartMessage": "Es fängt an zu schneien!", "snowLapseMessage": "<PERSON> Schneesturm tobt.", "snowClearMessage": "Der Schneesturm legt sich.", "fogStartMessage": "Am Boden breitet sich dichter N<PERSON>el aus!", "fogLapseMessage": "Der Nebel bleibt dicht.", "fogClearMessage": "Der Nebel lichtet sich.", "heavyRainStartMessage": "Es fängt an, in Strömen zu regnen!", "heavyRainLapseMessage": "Der strömende Regen hält an.", "heavyRainEffectMessage": "Der strömende Regen löscht die Feuer-Attacke und macht sie wirkungslos!", "heavyRainContinueMessage": "Der strömende Regen lässt nicht nach!", "heavyRainClearMessage": "Der strömende Regen lässt nach.", "harshSunStartMessage": "Das Sonnenlicht wird sehr viel stärker!", "harshSunLapseMessage": "<PERSON> Sonnenlicht ist sehr stark.", "harshSunEffectMessage": "Das intensive Sonnenlicht lässt die Wasser-Attacke verdampfen und macht sie wirkungslos!", "harshSunContinueMessage": "Das starke Sonnen<PERSON>t lässt nicht nach!", "harshSunClearMessage": "Das Sonnenlicht verliert an Intensität.", "strongWindsStartMessage": "Alle Flug-Pokémon werden von rätselhaften Luftströmungen geschützt!", "strongWindsLapseMessage": "Die rätselhafte Luftströmung hält an.", "strongWindsEffectMessage": "Rätselhafte Luftströmungen haben den Angriff abgeschwächt!", "strongWindsContinueMessage": "Die rätselhaften Luftströmungen lassen nicht nach!", "strongWindsClearMessage": "Die rätselhafte Luftströmung hat sich wieder gelegt!", "defaultEffectMessage": "Die Attacke wurde durch den Wettereffekt blockiert!"}