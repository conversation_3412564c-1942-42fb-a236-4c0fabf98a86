{"intro": "Da ist ein riesiger Beerenstrauch in der Nähe dieses Pokémons!", "title": "Überall Beeren", "description": "<PERSON><PERSON> sche<PERSON>, als ob ein starkes Pokémon einen Beerenstrauch bewacht. Ein Kampf wäre der direkte Weg, aber es sieht stark aus. Vielleicht könnte ein schnelles Pokémon ein paar Beeren schnappen, ohne erwischt zu werden?", "query": "Was wirst du tun?", "berries": "<PERSON><PERSON><PERSON>", "option": {"1": {"label": "<PERSON><PERSON><PERSON>nen", "tooltip": "(-) Schwerer Kampf\n(+) Beeren erhalten", "selected": "Du trittst dem Pokémon ohne Furcht entgegen."}, "2": {"label": "<PERSON><PERSON> Strauch rennen", "tooltip": "(-) {{fastest<PERSON><PERSON><PERSON>}} nutzt seine Geschwindigkeit\n(+) <PERSON><PERSON> erhalten", "selected": "Dein {{fastestPokemon}} rennt zum Strauch!$Es schafft es, {{numBerries}} zu schnap<PERSON>, bevor das {{enemyPokemon}} reagieren kann!$Du ziehst dich schnell mit deiner neuen Beute zurück.", "selected_bad": "Dein {{fastestPokemon}} rennt zum Strauch!$Oh nein! Das {{enemyPokemon}} war schneller und hat den Weg blockiert!", "boss_enraged": "Das gegnerische {{enemyPokemon}} ist wütend geworden!"}, "3": {"label": "Verlassen", "tooltip": "(-) <PERSON><PERSON>", "selected": "Du lässt das starke Pokémon mit seinem Item zurück und gehst weiter."}}}