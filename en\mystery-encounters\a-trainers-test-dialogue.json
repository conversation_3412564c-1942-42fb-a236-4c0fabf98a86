{"intro": "An extremely strong trainer approaches you…", "buck": {"intro_dialogue": "Yo, trainer! My name’s <PERSON>.$I have a super awesome proposal\nfor a strong trainer such as yourself!$I’m carrying two rare Pokémon Eggs with me,\nbut I’d like someone else to care for one.$If you can prove your strength as a trainer to me,\nI’ll give you the rarer egg!", "accept": "Whoooo, I’m getting fired up!", "decline": "<PERSON><PERSON>, it looks like your\nteam isn’t in peak condition.$Here, let me help with that."}, "cheryl": {"intro_dialogue": "Hello, my name’s <PERSON>.$I have a particularly interesting request,\nfor a strong trainer such as yourself.$I’m carrying two rare Pokémon Eggs with me,\nbut I’d like someone else to care for one.$If you can prove your strength as a trainer to me,\nI’ll give you the rarer Egg!", "accept": "I hope you’re ready!", "decline": "I understand, it looks like your team\nisn’t in the best condition at the moment.$Here, let me help with that."}, "marley": {"intro_dialogue": "…@d{64} I’m <PERSON>.$I have an offer for you…$I’m carrying two Pokémon Eggs with me,\nbut I’d like someone else to care for one.$If you’re stronger than me,\nI’ll give you the rarer Egg.", "accept": "… I see.", "decline": "… I see.$Your Pokémon look hurt…\nLet me help."}, "mira": {"intro_dialogue": "Hi! I’m <PERSON>!$<PERSON> has a request\nfor a strong trainer like you!$<PERSON> has two rare Pokémon Eggs,\nbut <PERSON> wants someone else to take one!$If you show <PERSON> that you’re strong,\n<PERSON> will give you the rarer Egg!", "accept": "You’ll battle <PERSON>?\nYay!", "decline": "Aww, no battle?\nThat’s okay!$Here, <PERSON> will heal your team!"}, "riley": {"intro_dialogue": "I’m Riley.$I have an odd proposal\nfor a strong trainer such as yourself.$I’m carrying two rare Pokémon Eggs with me,\nbut I’d like to give one to another trainer.$If you can prove your strength to me,\nI’ll give you the rarer Egg!", "accept": "That look you have…\nLet’s do this.", "decline": "I understand, your team looks beat up.$Here, let me help with that."}, "title": "A Trainer’s Test", "description": "It seems this trainer is willing to give you an Egg regardless of your decision. However, if you can manage to defeat this strong trainer, you’ll receive a much rarer Egg.", "query": "What will you do?", "option": {"1": {"label": "Accept the Challenge", "tooltip": "(-) Brutal Battle\n(+) Gain a @[TOOLTIP_TITLE]{Very Rare Egg}"}, "2": {"label": "Refuse the Challenge", "tooltip": "(+) Full Heal Party\n(+) Gain an @[TOOLTIP_TITLE]{Egg}"}}, "eggTypes": {"rare": "a Rare Egg", "epic": "an Epic Egg", "legendary": "a Legendary Egg"}, "outro": "{{statTrainer<PERSON>ame}} gave you {{eggType}}!"}