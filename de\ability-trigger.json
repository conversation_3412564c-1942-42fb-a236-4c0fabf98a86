{"blockRecoilDamage": "{{pokemonName}} wurde durch {{abilityName}} vor <PERSON><PERSON> geschützt!", "badDreams": "{{pokemon<PERSON>ame}} ist in einem Alptraum gefangen!", "costar": "{{pokemonName}} kopiert die Statusveränderungen von {{allyName}}!", "iceFaceAvoidedDamage": "{{pokemonNameWithAffix}} wehrt Schaden mit {{abilityName}} ab!", "perishBody": "Durch {{abilityName}} von {{pokemonName}} werden beide Pokémon nach drei Runden K.O. gehen!", "poisonHeal": "{{abilityName}} von {{pokemonName}} füllte einige KP auf!", "trace": "{{pokemonName}} kopiert {{abilityName}} von {{targetName}}!", "windPowerCharged": "Der Treffer durch {{moveName}} lädt die Stärke von {{pokemonName}} auf!", "quickDraw": "Durch <PERSON> kann {{pokemonName}} schneller handeln als sonst!", "illusionBreak": "<PERSON> Trugbild von {{pokemonName}} verschwindet!", "disguiseAvoidedDamage": "Die Tarnung von {{pokemonNameWithAffix}} ist aufgeflogen!", "blockItemTheft": "{{abilityName}} von {{pokemonNameWithAffix}} verhindert Item-Diebstahl!", "typeImmunityHeal": "{{abilityName}} von {{pokemonNameWithAffix}} füllte einige KP auf!", "nonSuperEffectiveImmunity": "{{pokemonNameWithAffix}} vermeidet Schaden mit {{abilityName}}!", "fullHpResistType": "Der Panzer von {{pokemonNameWithAffix}} funkelt und verzerrt die Wechselwirkungen zwischen den Typen!", "moveImmunity": "Es hat keine Wirkung auf {{pokemonNameWithAffix}}…", "reverseDrain": "{{pokemonNameWithAffix}} saugt Kloakensoße auf!", "postDefendTypeChange": "{{abilityName}} von {{pokemonNameWithAffix}} macht es zu einem {{typeName}}-Typ!", "postDefendContactDamage": "{{abilityName}} von {{pokemonNameWithAffix}} schadet seinem Ang<PERSON>ifer!", "postDefendAbilitySwap": "{{pokemonNameWithAffix}} tauscht Fähigkeiten mit dem Ziel!", "postDefendAbilityGive": "{{pokemonNameWithAffix}} gibt seinem Ziel {{abilityName}}!", "postDefendMoveDisable": "{{moveName}} von {{pokemonNameWithAffix}} wurde blockiert!", "pokemonTypeChange": "{{pokemonNameWithAffix}} nimmt den Typ {{moveType}} an!", "pokemonTypeChangeRevert": "{{pokemonNameWithAffix}} nimmt wieder seinen ursprünglichen Typ an!", "postAttackStealHeldItem": "{{pokemonNameWithAffix}} stiehlt {{stolenItemType}} von {{defenderName}}!", "postDefendStealHeldItem": "{{pokemonNameWithAffix}} stiehlt {{stolenItemType}} von {{attackerName}}!", "copyFaintedAllyAbility": "Die Fähigkeit {{abilityName}} von {{pokemonNameWithAffix}} wurde übernommen!", "intimidateImmunity": "{{abilityName}} von {{pokemonNameWithAffix}} verhindert den Effekt von Bedroher!", "postSummonAllyHeal": "{{pokemonNameWithAffix}} trinkt den von {{pokemonName}} zubereiteten Tee!", "postSummonClearAllyStats": "Die Statusveränderungen von {{pokemonNameWithAffix}} wurden aufgehoben!", "postSummonTransform": "{{pokemonNameWithAffix}} verwandelt sich in {{targetName}}!", "protectStat": "{{abilityName}} von {{pokemonNameWithAffix}} ver<PERSON><PERSON><PERSON>, dass der Statuswert {{statName}} gesenkt wird!", "statusEffectImmunityWithName": "{{abilityName}} von {{pokemonNameWithAffix}} verhindert {{statusEffectName}}!", "statusEffectImmunity": "{{abilityName}} von {{pokemonNameWithAffix}} verhindert Statusprobleme!", "battlerTagImmunity": "{{abilityName}} von {{pokemonNameWithAffix}} verhindert {{battlerTagName}}!", "typeImmunityPowerBoost": "Die Stärke der {{typeName}}-<PERSON><PERSON> {{pokemonNameWithAffix}} wurde erhöht!", "forewarn": "Vorwarnung von {{pokemonNameWithAffix}}: Konzentration auf {{moveName}}!", "frisk": "{{pokemonNameWithAffix}} hat die Fähigkeit {{opponentAbilityName}} von {{opponentName}} erschnüffelt!", "postWeatherLapseHeal": "{{abilityName}} von {{pokemonNameWithAffix}} füllte einige KP auf!", "postWeatherLapseDamage": "{{pokemonNameWithAffix}} wurde durch {{abilityName}} verletzt!", "postTurnLootCreateEatenBerry": "{{pokemonNameWithAffix}} hat {{berryName}} geerntet!", "postTurnHeal": "{{abilityName}} von {{pokemonNameWithAffix}} füllte einige KP auf!", "fetchBall": "{{pokemonNameWithAffix}} hat einen {{pokeballName}} gefunden!", "healFromBerryUse": "{{abilityName}} von {{pokemonNameWithAffix}} füllte einige KP auf!", "arenaTrap": "{{abilityName}} von {{pokemonNameWithAffix}} verhindert den Tausch!", "postBattleLoot": "{{pokemonNameWithAffix}} hebt {{itemName}} auf!", "postFaintContactDamage": "{{abilityName}} von {{pokemonNameWithAffix}} schadet seinem Ang<PERSON>ifer!", "postFaintHpDamage": "{{abilityName}} von {{pokemonNameWithAffix}} schadet seinem Ang<PERSON>ifer!", "postSummonPressure": "{{pokemonNameWithAffix}} setzt Gegner mit Erzwinger unter Druck!", "weatherEffectDisappeared": "Jegliche wetterbedingten Effekte wurden aufgehoben!", "postSummonMoldBreaker": "{{pokemonNameWithAffix}} gelingt es, gegnerische Fähigkeiten zu überbrücken!", "postSummonAnticipation": "{{pokemonNameWithAffix}} erschaudert!", "postSummonTurboblaze": "{{pokemonNameWithAffix}} strahlt eine lodernde Aura aus!", "postSummonTeravolt": "{{pokemonNameWithAffix}} strahlt eine knisternde Aura aus!", "postSummonDarkAura": "{{pokemonNameWithAffix}} strahlt eine dunkle Aura aus!", "postSummonFairyAura": "{{pokemonNameWithAffix}} strahlt eine Feenaura aus!", "postSummonAuraBreak": "{{pokemonNameWithAffix}} kehrt die Wirkung aller Aura-Fähigkeiten um!", "postSummonNeutralizingGas": "Reaktionsgas von {{pokemonNameWithAffix}} hat sich in der Umgebung ausgebreitet!", "postSummonAsOneGlastrier": "{{pokemonNameWithAffix}} verfügt über zwei Fähigkeiten!", "postSummonAsOneSpectrier": "{{pokemonNameWithAffix}} verfügt über zwei Fähigkeiten!", "postSummonVesselOfRuin": "Unheilsge<PERSON><PERSON>ß von {{pokemonNameWithAffix}} schwächt {{statName}} aller Pokémon im Umkreis!", "postSummonSwordOfRuin": "Unheilsschwert von {{pokemonNameWithAffix}} schwächt {{statName}} aller Pokémon im Umkreis!", "postSummonTabletsOfRuin": "Unheilstafeln von {{pokemonNameWithAffix}} schwächt {{statName}} aller Pokémon im Umkreis!", "postSummonBeadsOfRuin": "Unheilsjuwelen von {{pokemonNameWithAffix}} schwächt {{statName}} aller Pokémon im Umkreis!", "preventBerryUse": "{{pokemonNameWithAffix}} kriegt vor Anspannung keine Beeren mehr runter!"}