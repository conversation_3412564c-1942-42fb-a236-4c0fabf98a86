{"boy": "<PERSON><PERSON>", "girl": "<PERSON><PERSON><PERSON><PERSON>", "general": "Allgemein", "display": "Anzeige", "audio": "Audio", "gamepad": "Controller", "keyboard": "Tastatur", "gameSpeed": "Spielgeschwindigkeit", "gameSpeed1x": "1x", "gameSpeed1_25x": "1,25x", "gameSpeed1_5x": "1,5x", "gameSpeed2x": "2x", "gameSpeed2_5x": "2,5x", "gameSpeed3x": "3x", "gameSpeed4x": "4x", "gameSpeed5x": "5x", "hpBarSpeed": "KP-Balken Geschw.", "expGainsSpeed": "EP-<PERSON><PERSON><PERSON>.", "expPartyDisplay": "Team-EP anzeigen", "skipSeenDialogues": "Gesehenen Dialog überspringen", "eggSkip": "Ei-Zusammenfassung", "never": "<PERSON><PERSON>", "always": "Immer", "ask": "Fragen", "battleStyle": "<PERSON><PERSON><PERSON><PERSON>", "enableRetries": "Erneut versuchen aktivieren", "hideIvs": "IS-<PERSON><PERSON><PERSON>", "tutorials": "Tutorials", "touchControls": "Touch Steuerung", "vibrations": "Vibration", "normal": "Normal", "fast": "<PERSON><PERSON><PERSON>", "faster": "<PERSON><PERSON><PERSON>", "skip": "Überspringen", "levelUpNotifications": "Nur Lvl.-Up", "on": "An", "off": "Aus", "switch": "<PERSON><PERSON><PERSON>", "set": "<PERSON><PERSON><PERSON>", "auto": "Auto", "disabled": "Deaktiviert", "language": "<PERSON><PERSON><PERSON>", "change": "Ändern", "uiTheme": "UI Thema", "default": "Standard", "legacy": "Legacy", "windowType": "Fenster Typ", "moneyFormat": "Währungsformat", "damageNumbers": "Schadensnummern", "simple": "<PERSON><PERSON><PERSON>", "fancy": "<PERSON><PERSON><PERSON><PERSON>", "abbreviated": "Abgekürzt", "moveAnimations": "Attacken Animationen", "showStatsOnLevelUp": "<PERSON><PERSON> beim <PERSON> anzeigen", "candyUpgradeNotification": "Bonbon Upgrade Benachrichtigung", "passivesOnly": "Nur Passive", "candyUpgradeDisplay": "Bonbon Upgrade Anzeige", "icon": "Icon", "animation": "Animation", "moveInfo": "Attacken-Info", "showMovesetFlyout": "Zeige Attacken Flyout", "showArenaFlyout": "Zeige Arena Flyout", "showTimeOfDayWidget": "Zeige Tageszeit Widget", "timeOfDayAnimation": "Tageszeit Animation", "bounce": "Springen", "timeOfDay_back": "Zurück", "spriteSet": "Sprite Satz", "consistent": "Konsistent", "experimental": "Experimentel", "fusionPaletteSwaps": "Fusion-Farbpalettenwechsel", "playerGender": "Spielergeschlecht", "typeHints": "Typhinweise", "masterVolume": "Gesamtlautstärke", "bgmVolume": "Hintergrundmusik", "fieldVolume": "Rufe & Attacken", "seVolume": "Spezialeffekte", "uiVolume": "Benutzeroberfläche", "battleMusic": "Kampfmusik", "musicGenFive": "5. Gen.", "musicAllGens": "Alle Gen.", "gamepadPleasePlug": "Bitte einen Controller anschließen oder eine Taste drücken.", "delete": "Löschen", "keyboardPleasePress": "Bitte eine Taste auf der Tastatur drücken.", "reset": "Reset", "requireReload": "Neuladen", "action": "Aktion", "back": "Zurück", "pressToBind": "<PERSON><PERSON> Zuweisen drücken", "pressButton": "Eine Taste drücken…", "assignButton": "<PERSON><PERSON><PERSON>", "buttonUp": "Hoch", "buttonDown": "<PERSON>ter", "buttonLeft": "Links", "buttonRight": "<PERSON><PERSON><PERSON>", "buttonAction": "Aktion", "buttonMenu": "<PERSON><PERSON>", "buttonSubmit": "Bestätigen", "buttonCancel": "Abbrechen", "buttonStats": "Statuswerte", "buttonCycleForm": "Form wechseln", "buttonCycleShiny": "Schillernd wechseln", "buttonCycleGender": "Geschlecht wechseln", "buttonCycleAbility": "Fähigkeit wechseln", "buttonCycleNature": "Wesen wechseln", "buttonCycleVariant": "<PERSON><PERSON><PERSON> we<PERSON>n", "buttonCycleTera": "Tera-<PERSON><PERSON>", "buttonSpeedUp": "Beschleunigen", "buttonSlowDown": "Verlangsamen", "alt": " (Alt)", "mute": "Stumm", "controller": "Controller", "controllerDefault": "Standard", "controllerChange": "Ändern", "gamepadSupport": "Controllerunterstützung", "gamepadSupportAuto": "Auto", "gamepadSupportDisabled": "Deaktiviert", "pressActionToAssign": "Aktion zum Zuweisen drücken", "willSwapWith": "tauscht mit", "confirmSwap": "Tausch bestätigen", "cancelContollerChoice": "Abbrechen", "showBgmBar": "Musiknamen anzeigen", "hideUsername": "<PERSON>utzern<PERSON> verst<PERSON>", "moveTouchControls": "Bewegung Touch Steuerung", "touchReset": "Reset", "touchSaveClose": "Speichern & Beenden", "touchCancel": "Abbrechen", "orientation": "Orientierung:", "landscape": "Querformat", "portrait": "Hochformat", "shopOverlayOpacity": "Shop Overlay Deckkraft", "shopCursorTarget": "Shop-<PERSON><PERSON><PERSON>", "commandCursorMemory": "Kampfzeigerposition merken", "rewards": "Items", "reroll": "<PERSON><PERSON> rollen", "shop": "Shop", "checkTeam": "Team überprüfen", "confirmDisableTouch": "Möchten Sie die Touch-Steuerung wirklich deaktivieren?", "defaultConfirmMessage": "Sind Sie sicher?"}