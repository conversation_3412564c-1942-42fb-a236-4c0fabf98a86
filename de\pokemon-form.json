{"pikachu": "Normal", "pikachuCosplay": "Cosplay", "pikachuCoolCosplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikachuBeautyCosplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikachuCuteCosplay": "Star-<PERSON><PERSON><PERSON>", "pikachuSmartCosplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikachuToughCosplay": "Wrestler-<PERSON><PERSON><PERSON>", "pikachuPartner": "Partner-<PERSON><PERSON><PERSON>", "eevee": "Normal", "eeveePartner": "Partner-E<PERSON><PERSON>", "pichu": "Normal", "pichuSpiky": "Strubbelohr-Pichu", "unownA": "A", "unownB": "B", "unownC": "C", "unownD": "D", "unownE": "E", "unownF": "F", "unownG": "G", "unownH": "H", "unownI": "I", "unownJ": "J", "unownK": "K", "unownL": "L", "unownM": "M", "unownN": "N", "unownO": "O", "unownP": "P", "unownQ": "Q", "unownR": "R", "unownS": "S", "unownT": "T", "unownU": "U", "unownV": "V", "unownW": "W", "unownX": "X", "unownY": "Y", "unownZ": "Z", "unownExclamation": "!", "unownQuestion": "?", "castform": "Normalform", "castformSunny": "Sonnenform", "castformRainy": "Regenform", "castformSnowy": "Schneeform", "deoxysNormal": "Normalform", "deoxysAttack": "Angriffsform", "deoxysDefense": "Verteidigungsform", "deoxysSpeed": "Initiativeform", "burmyPlant": "Pflanzenumhang", "burmySandy": "<PERSON><PERSON><PERSON>", "burmyTrash": "Lumpenumhang", "cherubiOvercast": "Wolkenform", "cherubiSunshine": "Sonnenform", "shellosEast": "Öst<PERSON><PERSON> Meer", "shellosWest": "West<PERSON><PERSON>", "rotom": "Normalform", "rotomHeat": "Hitze-Rotom", "rotomWash": "Wasch-R<PERSON>m", "rotomFrost": "Frost<PERSON><PERSON><PERSON><PERSON>", "rotomFan": "Wirbel-Rotom", "rotomMow": "Schneid-Rotom", "dialga": "Normalform", "dialgaOrigin": "Urform", "palkia": "Normalform", "palkiaOrigin": "Urform", "giratinaAltered": "Wandelform", "giratinaOrigin": "Urform", "shayminLand": "Landform", "shayminSky": "Zenitform", "basculinRedStriped": "Rotlinige Form", "basculinBlueStriped": "Blaulinige Form", "basculinWhiteStriped": "Weißlinige Form", "darumaka": "Normalmodus", "darumakaZen": "Trance-Modus", "deerlingSpring": "Frühlingsform", "deerlingSummer": "Sommerform", "deerlingAutumn": "Herbstform", "deerlingWinter": "Winterform", "tornadusIncarnate": "Inkarnationsform", "tornadusTherian": "Tiergeistform", "thundurusIncarnate": "Inkarnationsform", "thundurusTherian": "Tiergeistform", "landorusIncarnate": "Inkarnationsform", "landorusTherian": "Tiergeistform", "kyurem": "Normal", "kyuremBlack": "<PERSON><PERSON><PERSON><PERSON>", "kyuremWhite": "Weißes Kyurem", "keldeoOrdinary": "Standardform", "keldeoResolute": "Resolutform", "meloettaAria": "Gesangsform", "meloettaPirouette": "Tanzform", "genesect": "Normal", "genesectShock": "Blitzmodul", "genesectBurn": "Flammenmodul", "genesectChill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "genesectDouse": "Aquamodul", "froakie": "Normalform", "froakieBattleBond": "Freundschaftsakt", "froakieAsh": "Ash-Form", "scatterbugMeadow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scatterbugIcySnow": "Frostmuster", "scatterbugPolar": "Schneefeldmuster", "scatterbugTundra": "Flockenmuster", "scatterbugContinental": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scatterbugGarden": "Ziergartenmuster", "scatterbugElegant": "Prunkmuster", "scatterbugModern": "Innovationsmuster", "scatterbugMarine": "Aquamarinmuster", "scatterbugArchipelago": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scatterbugHighPlains": "D<PERSON>rrmus<PERSON>", "scatterbugSandstorm": "Sandmuster", "scatterbugRiver": "Flussdeltamuster", "scatterbugMonsoon": "Monsunmuster", "scatterbugSavanna": "Savannenmuster", "scatterbugSun": "Sonnenmuster", "scatterbugOcean": "<PERSON><PERSON><PERSON><PERSON>", "scatterbugJungle": "Dschungelmuster", "scatterbugFancy": "Fantasiemuster", "scatterbugPokeBall": "Pokéball-<PERSON><PERSON>", "flabebeRed": "Rotblütler", "flabebeYellow": "Gel<PERSON><PERSON>ü<PERSON>", "flabebeOrange": "Orangeblütler", "flabebeBlue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flabebeWhite": "Weißblütler", "furfrou": "Zottelform", "furfrouHeart": "Herzchenschnitt", "furfrouStar": "Sternchenschnitt", "furfrouDiamond": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "furfrouDebutante": "Fräuleinschnitt", "furfrouMatron": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "furfrouDandy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "furfrouLaReine": "Königinnenschnitt", "furfrouKabuki": "Kabuki-<PERSON><PERSON><PERSON>", "furfrouPharaoh": "Herrscherschnitt", "espurrMale": "<PERSON><PERSON><PERSON><PERSON>", "espurrFemale": "<PERSON><PERSON><PERSON>", "honedgeShield": "Schildform", "honedgeBlade": "Klingenform", "pumpkaboo": "Größe M", "pumpkabooSmall": "Größe S", "pumpkabooLarge": "Größe L", "pumpkabooSuper": "Größe XL", "xerneasNeutral": "Ruhe-Modus", "xerneasActive": "Aktiv-Modus", "zygarde50": "50% Form", "zygarde10": "10% Form", "zygarde50Pc": "50% Form Scharwandel", "zygarde10Pc": "10% Form Scharwandel", "zygardeComplete": "Optimum-Form", "hoopa": "Gebanntes <PERSON>", "hoopaUnbound": "Entfesseltes Hoopa", "oricorioBaile": "Flamenco-Stil", "oricorioPompom": "Cheerleading-Stil", "oricorioPau": "Hula-Stil", "oricorioSensu": "Buyo-Stil", "rockruff": "Normalform", "rockruffOwnTempo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rockruffMidday": "Tagform", "rockruffMidnight": "Nachtform", "rockruffDusk": "Zwielichtform", "wishiwashi": "Einzelform", "wishiwashiSchool": "Schwarmform", "typeNullNormal": "Typ:Normal", "typeNullFighting": "Typ:<PERSON><PERSON><PERSON>", "typeNullFlying": "Typ:Flug", "typeNullPoison": "Typ:Gift", "typeNullGround": "Typ:<PERSON><PERSON>", "typeNullRock": "Typ:<PERSON><PERSON><PERSON>", "typeNullBug": "Typ:<PERSON><PERSON><PERSON>", "typeNullGhost": "Typ:<PERSON><PERSON><PERSON>", "typeNullSteel": "Typ:<PERSON><PERSON>", "typeNullFire": "Typ:<PERSON><PERSON>", "typeNullWater": "Typ:<PERSON><PERSON>", "typeNullGrass": "Typ:<PERSON><PERSON><PERSON><PERSON>", "typeNullElectric": "Typ:Elek<PERSON>", "typeNullPsychic": "Typ:<PERSON><PERSON><PERSON>", "typeNullIce": "Typ:<PERSON><PERSON>", "typeNullDragon": "Typ:<PERSON><PERSON>", "typeNullDark": "Typ:<PERSON><PERSON><PERSON>", "typeNullFairy": "Typ:<PERSON><PERSON>", "miniorRedMeteor": "Rote-Meteorform", "miniorOrangeMeteor": "Oranger-Meteorform", "miniorYellowMeteor": "Gelber-Meteorform", "miniorGreenMeteor": "Grüner-Meteorform", "miniorBlueMeteor": "Hellblaue-Meteorform", "miniorIndigoMeteor": "Blaue-Meteorform", "miniorVioletMeteor": "Violette-Meteorform", "miniorRed": "<PERSON><PERSON>", "miniorOrange": "<PERSON><PERSON>", "miniorYellow": "<PERSON><PERSON><PERSON>", "miniorGreen": "<PERSON><PERSON><PERSON><PERSON>", "miniorBlue": "<PERSON><PERSON><PERSON> Kern", "miniorIndigo": "<PERSON><PERSON><PERSON>", "miniorViolet": "<PERSON><PERSON>", "mimikyuDisguised": "Verkleidete Form", "mimikyuBusted": "Entlarvte Form", "cosmog": "Normalform", "cosmogRadiantSun": "Sonnenaufgang", "cosmogFullMoon": "Vollmond", "necrozma": "Normalform", "necrozmaDuskMane": "Abendmähne", "necrozmaDawnWings": "Morgenschwingen", "necrozmaUltra": "Ultra-Necrozma", "magearna": "Normalform", "magearnaOriginal": "Originalfarbe", "marshadow": "Normalform", "marshadowZenith": "Zenitform", "cramorant": "Normalform", "cramorantGulping": "Schlingform", "cramorantGorging": "Stopfform", "toxelAmped": "Hoch-Form", "toxelLowkey": "Tief-Form", "sinisteaPhony": "Fälschungsform", "sinisteaAntique": "Originalform", "milceryVanillaCream": "Vanille-Creme", "milceryRubyCream": "Ruby-Creme", "milceryMatchaCream": "Matcha-Creme", "milceryMintCream": "Minz-Creme", "milceryLemonCream": "Zitronen-Creme", "milcerySaltedCream": "Salz-Creme", "milceryRubySwirl": "Ruby-Mix", "milceryCaramelSwirl": "Karamell-Mix", "milceryRainbowSwirl": "Trio-Mix", "eiscue": "Tiefkühlkopf", "eiscueNoIce": "<PERSON><PERSON>lfühlkopf", "indeedeeMale": "<PERSON><PERSON><PERSON><PERSON>", "indeedeeFemale": "<PERSON><PERSON><PERSON>", "morpekoFullBelly": "Pappsattmuster", "morpekoHangry": "Kohldampfmuster", "zacianHeroOfManyBattles": "Heldenhafter Krieger", "zacianCrowned": "König des Schwertes", "zamazentaHeroOfManyBattles": "Heldenhafter Krieger", "zamazentaCrowned": "König des Schildes", "kubfuSingleStrike": "Fokussierter Stil", "kubfuRapidStrike": "Fließender Stil", "zarude": "Normalform", "zarudeDada": "<PERSON>", "calyrex": "Normalform", "calyrexIce": "Sc<PERSON>melreiter", "calyrexShadow": "Rappenreiter", "basculinMale": "<PERSON><PERSON><PERSON><PERSON>", "basculinFemale": "<PERSON><PERSON><PERSON>", "enamorusIncarnate": "Inkarnationsform", "enamorusTherian": "Tiergeistform", "lechonkMale": "<PERSON><PERSON><PERSON><PERSON>", "lechonkFemale": "<PERSON><PERSON><PERSON>", "tandemausFour": "Dreierfamilie", "tandemausThree": "Viererfamilie", "squawkabillyGreenPlumage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "squawkabillyBluePlumage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "squawkabillyYellowPlumage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "squawkabillyWhitePlumage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finizenZero": "Alltagsform", "finizenHero": "Heldenform", "revavroomSeginStarmobile": "Segin-Starmobil", "revavroomSchedarStarmobile": "Schedir-Starmobil", "revavroomNaviStarmobile": "Tsih-Starmobil", "revavroomRuchbahStarmobile": "Rukbat-Starmobil", "revavroomCaphStarmobile": "Caph-Starmobil", "tatsugiriCurly": "Gebogene Form", "tatsugiriDroopy": "Hängende Form", "tatsugiriStretchy": "Gestrekte Form", "dunsparceTwoSegment": "Zweisegmentform", "dunsparceThreeSegment": "Dreisegmentform", "gimmighoulChest": "Truhenform", "gimmighoulRoaming": "Wanderform", "koraidonApexBuild": "Vollkommene Gestalt", "koraidonLimitedBuild": "Gehemmte Gestalt", "koraidonSprintingBuild": "Sprintgestalt", "koraidonSwimmingBuild": "Schwimmgestalt", "koraidonGlidingBuild": "Schwingengestalt", "miraidonUltimateMode": "Kompletter Modus", "miraidonLowPowerMode": "Begrenzter Modus", "miraidonDriveMode": "Fahrmodus", "miraidonAquaticMode": "Wassermodus", "miraidonGlideMode": "Gleit<PERSON><PERSON>", "poltchageistCounterfeit": "Imitationsform", "poltchageistArtisan": "Kostbarkeitsform", "poltchageistUnremarkable": "Simple Form", "poltchageistMasterpiece": "<PERSON><PERSON>", "ogerponTealMask": "Türkisgrüne Maske", "ogerponTealMaskTera": "Türkisg<PERSON><PERSON><PERSON> (Terakristallisiert)", "ogerponWellspringMask": "Brunnenmaske", "ogerponWellspringMaskTera": "Brunnenmaske (Terakristallisiert)", "ogerponHearthflameMask": "Ofenmaske", "ogerponHearthflameMaskTera": "Ofenmaske (Terakristallisiert)", "ogerponCornerstoneMask": "Fundamentmaske", "ogerponCornerstoneMaskTera": "Fundamentmaske (Terakristallisiert)", "terapagos": "Normalform", "terapagosTerastal": "Terakristall-Form", "terapagosStellar": "Stellarform", "galarDarumaka": "Normalmodus", "galarDarumakaZen": "Trance-Modus", "paldeaTaurosCombat": "Gefechtsvariante", "paldeaTaurosBlaze": "Flammenvariante", "paldeaTaurosAqua": "Flutenvariante", "floetteEternalFlower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ursalunaBloodmoon": "<PERSON><PERSON><PERSON>", "regionalForm": {"ALOLA": "Alola-Form", "GALAR": "Galar-Form", "HISUI": "Hisui-<PERSON>", "PALDEA": "Paldea-Form"}, "appendForm": {"GENERIC": "{{pokemonName}} ({{formName}})", "ALOLA": "Alola-{{pokemon<PERSON><PERSON>}}", "GALAR": "Galar-{{pokemon<PERSON><PERSON>}}", "HISUI": "Hisui-{{pokemon<PERSON><PERSON>}}", "PALDEA": "Paldea-{{pokemonName}}", "ETERNAL": "{{poke<PERSON><PERSON><PERSON>}} (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "BLOODMOON": "<PERSON><PERSON><PERSON>-{{pokemon<PERSON>ame}}"}, "battleForm": {"mega": "Mega", "mega-x": "Mega-X", "mega-y": "Mega-Y", "primal": "Protomorphose", "gigantamax": "Gigadynamax", "gigantamax-single": "Gigadynamax (Fokussiert)", "gigantamax-rapid": "Gigadynamax (Fließend)", "eternamax": "Unendynamax"}}