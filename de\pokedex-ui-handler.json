{"BASE_STATS": "Basiswerte", "ABILITIES": "Fähigkeiten", "LEVEL_MOVES": "Level-Attacken", "EGG_MOVES": "Ei-Attacken", "TM_MOVES": "TM-Attacken", "BIOMES": "Biome", "NATURES": "<PERSON><PERSON>", "TOGGLE_IVS": "DVs anzeigen/verbergen", "EVOLUTIONS": "Entwicklungen", "confirmExit": "<PERSON>t du zurück?", "showNature": "Freigeschaltetes Wesen anzeigen.", "showBaseStats": "Freigeschaltete Basiswerte anzeigen.", "showAbilities": "Zeige die freigeschalteten Fähigkeiten und Passive an.", "showLevelMoves": "Zeige die Attacken an die durch Levelaufstieg erlernt werden können.", "onlyEvolutionMove": "Diese Attacke wird nach der Entwicklung erlernt.", "onlyRecallMove": "An diese Attacke kann sich per Erinnerungspilz erinnert werden.", "onStarterSelectMove": "Diese Attacke kann zu Beginn eines Durchlaufs ausgewählt werden.", "byLevelUpMove": "Dies ist eine Level-Up Attacke.", "showEggMoves": "Zeige die Ei-Attacken an.", "showTmMoves": "Zeige die Attacken an die durch TMs erlernt werden können.", "showBiomes": "Zeige Habitate des Pokémon an.", "showNatures": "Freigeschaltetes Wesen anzeigen.", "showEvolutions": "Zeige Entwicklungen und Kampfformen an.", "noEvolutions": "Dieses Pokémon hat keine Entwicklungen oder Kampfformen.", "noBiomes": "Dieses Pokémon hat kein bekanntes Habitat.", "noEggMoves": "Dieser Starter verfügt über keine Ei-Attacken.", "noTmMoves": "Dieses Pokémon kann keine TM erlernen!", "baseTotal": "Gesamt", "common": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "rare": "Selten:", "hidden": "Versteckt:", "prevolutions": "Vorentwicklungen:", "evolutions": "Entwicklungen:", "forms": "Formen:", "preBiomes": "Als Entwicklung:", "GREAT": "Super", "ULTRA": "Hyper", "ROGUE": "Rogue", "scanChooseOption": "<PERSON><PERSON>hle eine Option", "scanSelect": "Auswählen", "scanCancel": "Abbrechen", "scanLabelName": "Name e<PERSON>ben", "scanLabelMove": "<PERSON><PERSON> e<PERSON>ben", "scanLabelAbility": "Fähigkeit eingeben", "scanLabelPassive": "Passive eingeben", "goFilters": ": <PERSON><PERSON>", "toggleDecorations": ": Dekoration umschalten", "showForms": ": Formen anzeigen", "pokemonNumber": "Nr. ", "cycleShiny": ": <PERSON><PERSON><PERSON><PERSON>", "cycleForm": ": Form", "cycleGender": ": Geschlecht", "cycleVariant": ": Seltenheit", "candyUpgrade": ": Bonbon Upgrade kaufen", "showBackSprite": ": Rückseite anzeigen", "showFrontSprite": ": Vorderseite anzeigen", "gen1": "I", "gen2": "II", "gen3": "III", "gen4": "IV", "gen5": "V", "gen6": "VI", "gen7": "VII", "gen8": "VIII", "gen9": "IX", "growthRate": "Wachstum:", "ability": "Fähigkeit:", "passive": "Passiv:", "nature": "Wesen:", "eggMoves": "Ei-Attacken", "addToParty": "Zum Team hinzufügen", "removeFromParty": "Aus Team entfernen", "toggleIVs": "Zeige seine IS-Werte an.", "useCandies": "Bonbons verwenden", "unlockPassive": "Passiv-<PERSON><PERSON> freis<PERSON>", "reduceCost": "Preis reduzieren", "sameSpeciesEgg": "Ein Ei kaufen", "locked": "<PERSON><PERSON><PERSON><PERSON>", "disabled": "Deaktiviert", "uncaught": "Nicht gefangen"}