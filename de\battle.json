{"bossAppeared": "{{boss<PERSON>ame}} erscheint.", "trainerAppeared": "{{<PERSON><PERSON><PERSON>}} möchte kämpfen!", "trainerAppearedDouble": "{{<PERSON><PERSON><PERSON>}} möchten kämpfen!", "trainerSendOut": "{{trainerName}} setzt {{pokemonName}} ein!", "singleWildAppeared": "Ein wildes {{pokemon<PERSON>ame}} erscheint!", "multiWildAppeared": "Ein wildes {{pokemonName1}} und {{pokemonName2}} er<PERSON><PERSON>!", "playerComeBack": "<PERSON><PERSON> zurück, {{pokemon<PERSON><PERSON>}}!", "trainerComeBack": "{{trainerName}} ruft {{pokemonName}} zurück!", "playerGo": "Los! {{pokemon<PERSON>ame}}!", "trainerGo": "{{trainerName}} sendet {{pokemonName}} raus!", "pokemonDraggedOut": "{{pokemon<PERSON>ame}} wurde ausgewählt!", "switchQuestion": "Möchtest du {{pokemonName}} auswechseln?", "trainerDefeated": "{{<PERSON><PERSON><PERSON>}} wurde besiegt!", "moneyWon": "Du gewinnst {{moneyAmount}} ₽!", "moneyPickedUp": "<PERSON> {{moneyAmount}} ₽ auf!", "pokemonCaught": "{{pokemon<PERSON>ame}} wurde gefangen!", "pokemonObtained": "Du erhältst {{pokemonName}}!", "pokemonBrokeFree": "Mist!\nDas Pokémon hat sich befreit!", "pokemonFled": "<PERSON> wilde {{pokemon<PERSON><PERSON>}} ist geflohen!", "playerFled": "Du bist vor dem wilden {{pokemonName}} gef<PERSON>hen!", "addedAsAStarter": "{{pokemon<PERSON>ame}} wurde als Starterpokémon hinzugefügt!", "partyFull": "Dein Team ist voll. Möchtest du ein Pokémon durch {{pokemonName}} ersetzen?", "pokemon": "Pokémon", "sendOutPokemon": "Los, {{pokemon<PERSON><PERSON>}}!", "hitResultCriticalHit": "Ein Volltreffer!", "hitResultSuperEffective": "Das ist sehr effektiv!", "hitResultNotVeryEffective": "Das ist nicht sehr effektiv…", "hitResultNoEffect": "Es hat keine Wirkung auf {{pokemonName}}…", "hitResultImmune": "{{poke<PERSON><PERSON><PERSON>}} ist unversehrt!", "hitResultOneHitKO": "Ein K.O.-Treffer!", "attackFailed": "Es ist fehlgeschlagen!", "attackMissed": "Die Attacke hat {{pokemonNameWithAffix}} verfehlt!", "attackHitsCount": "{{count}}-mal getroffen!", "rewardGain": "Du erhältst {{modifierName}}!", "rewardGainCount": "Du erhältst {{count}} {{modifierName}}!", "expGain": "{{pokemonName}} erhält {{exp}} Erfahrungspunkte!", "levelUp": "{{pokemonName}} erreicht Lv. {{level}}!", "learnMove": "{{pokemonName}} erlernt {{moveName}}!", "learnMovePrompt": "{{pokemonName}} versuch<PERSON>, {{moveName}} zu er<PERSON><PERSON>.", "learnMoveLimitReached": "Aber {{poke<PERSON><PERSON><PERSON>}} kann nur maximal vier <PERSON><PERSON> er<PERSON>.", "learnMoveReplaceQuestion": "Soll eine bekannte Attacke durch {{moveName}} ersetzt werden?", "learnMoveStopTeaching": "{{moveName}} nicht erlernen?", "learnMoveNotLearned": "{{pokemonName}} hat {{moveName}} nicht erlernt.", "learnMoveForgetQuestion": "Welche Attacke soll vergessen werden?", "learnMoveForgetSuccess": "{{pokemonName}} hat {{moveName}} vergessen.", "countdownPoof": "@d{32}<PERSON><PERSON>, @d{15}zwei @d{15}und@d{15}… @d{15}… @d{15}… @d{15}@s{se/pb_bounce_1}schwupp!", "learnMoveAnd": "Und…", "levelCapUp": "Die Levelbeschränkung wurde auf {{levelCap}} erhöht!", "moveNotImplemented": "{{moveName}} ist noch nicht implementiert und kann nicht ausgewählt werden.", "moveNoPP": "Es sind keine AP für diese Attacke mehr übrig!", "moveDisabled": "{{moveName}} ist deaktiviert!", "moveDisabledTorment": "{{pokemonNameWithAffix}} kann aufgrund von Folterknecht die Attacke nicht zweimal hintereinander einsetzen!", "moveDisabledTaunt": "{{pokemonNameWithAffix}} kann {{moveName}} nach Verhöhner nicht einsetzen!", "moveDisabledHealBlock": "{{pokemonNameWithAffix}} kann {{moveName}} aufgrund von {{healBlockName}} nicht einsetzen.", "moveDisabledImprison": "{{pokemonNameWithAffix}} kann die versiegelte Attacke {{moveName}} nicht einsetzen!", "canOnlyUseMove": "{{pokemonName}} kann keine andere Attacke als {{moveName}} einsetzen!", "moveCannotBeSelected": "{{moveName}} kann nicht ausgewählt werden!", "disableInterruptedMove": "{{moveName}} von {{pokemonNameWithAffix}} ist blockiert!", "throatChopInterruptedMove": "{{pokemon<PERSON>ame}} kann die Attacke durch die Wirkung von Neck Strike nicht einsetzen!", "noPokeballForce": "Eine unsichtbare Kraft verhindert die Nutzung von Pokébällen.", "noPokeballTrainer": "Du kannst das Pokémon eines anderen Trainers nicht fangen!", "noPokeballMulti": "Du kannst erst einen Pokéball werfen, wenn nur noch ein Pokémon übrig ist!", "noPokeballStrong": "Das Ziel-Pokémon ist zu stark, um gefangen zu werden! Du musst es zuerst schwächen!", "noPokeballMysteryEncounter": "Du kannst dieses Pokémon nicht fangen!", "noEscapeForce": "Eine unsichtbare Kraft verhindert die Flucht.", "noEscapeTrainer": "Du kannst nicht aus einem Trainerkampf fliehen!", "noEscapePokemon": "{{moveName}} von {{pokemonName}} verhindert {{escapeVerb}}!", "noEscapeSwitch": "Das Pokémon kann nicht ausgetauscht werden!", "noEscapeFlee": "Das Pokémon kann nicht fliehen!", "runAwaySuccess": "Du bist entkommen!", "runAwayCannotEscape": "Flucht gescheitert!", "escapeVerbSwitch": "auswechseln", "escapeVerbFlee": "flucht", "notDisabled": "{{moveName}} von {{pokemonName}} ist nicht mehr deaktiviert!", "turnEndHpRestore": "Die KP von {{pokemonName}} wurden wiederhergestellt.", "hpIsFull": "Die KP von {{pokemonName}} sind voll!", "skipItemQuestion": "B<PERSON> du sicher, dass du kein Item nehmen willst?", "itemStackFull": "Du hast bereits zu viele von {{fullItemName}}. Du erhältst stattdessen {{itemName}}.", "eggHatching": "Oh?", "eggSkipPrompt": "{{eggsToHatch}} Eier sind bereit zu schlüpfen. Zur Ei-Zusammenfassung springen?", "ivScannerUseQuestion": "IV-<PERSON><PERSON><PERSON> auf {{pokemon<PERSON><PERSON>}} ben<PERSON><PERSON>?", "wildPokemonWithAffix": "{{poke<PERSON><PERSON><PERSON>}} (wild)", "foePokemonWithAffix": "{{poke<PERSON><PERSON><PERSON>}} (<PERSON><PERSON><PERSON>)", "useMove": "{{pokemonNameWithAffix}} setzt {{moveName}} ein!", "magicCoatActivated": "{{pokemonNameWithAffix}} leitet {{moveName}} zurück!", "drainMessage": "{{pokemon<PERSON>ame}} wurde Energie abgesaugt", "regainHealth": "<PERSON><PERSON> von {{pokemon<PERSON>ame}} wurden wieder aufgefrischt!", "stealEatBerry": "{{pokemonName}} hat {{targetName}} seine {{berryName}} weggefuttert!", "ppHealBerry": "{{berryName}} von {{pokemonNameWithAffix}} füllt AP von {{moveName}} auf!", "hpHealBerry": "{{berryName}} füllt KP von {{pokemonNameWithAffix}} auf!", "fainted": "{{pokemonNameWithAffix}} wurde besiegt!", "statsAnd": "und", "stats": "Alle Werte", "statRose_one": "{{stats}} von {{pokemonNameWithAffix}} steigt!", "statRose_other": "{{stats}} von {{pokemonNameWithAffix}} steigen!", "statSharplyRose_one": "{{stats}} von {{pokemonNameWithAffix}} steigt stark!", "statSharplyRose_other": "{{stats}} von {{pokemonNameWithAffix}} steigen stark!", "statRoseDrastically_one": "{{stats}} von {{pokemonNameWithAffix}} steigt drastisch!", "statRoseDrastically_other": "{{stats}} von {{pokemonNameWithAffix}} steigen drastisch!", "statWontGoAnyHigher_one": "{{stats}} von {{pokemonNameWithAffix}} kann nicht weiter erhöht werden!", "statWontGoAnyHigher_other": "{{stats}} von {{pokemonNameWithAffix}} können nicht weiter erhöht werden!", "statFell_one": "{{stats}} von {{pokemonNameWithAffix}} sinkt!", "statFell_other": "{{stats}} von {{pokemonNameWithAffix}} sinken!", "statHarshlyFell_one": "{{stats}} von {{pokemonNameWithAffix}} sinkt stark!", "statHarshlyFell_other": "{{stats}} von {{pokemonNameWithAffix}} sinken stark!", "statSeverelyFell_one": "{{stats}} von {{pokemonNameWithAffix}} sinkt drastisch!", "statSeverelyFell_other": "{{stats}} von {{pokemonNameWithAffix}} sinken drastisch!", "statWontGoAnyLower_one": "{{stats}} von {{pokemonNameWithAffix}} kann nicht weiter sinken!", "statWontGoAnyLower_other": "{{stats}} von {{pokemonNameWithAffix}} können nicht weiter sinken!", "transformedIntoType": "{{pokemonName}} transformed\ninto the {{type}} type!", "retryBattle": "Möchtest du vom Beginn des Kampfes neustarten?", "unlockedSomething": "{{unlockedThing}} wurde freigeschaltet.", "congratulations": "Glückwunsch!", "beatModeFirstTime": "{{speciesName}} hat den {{gameMode}} Modus zum ersten Mal beendet! Du erhältst {{newModifier}}!", "ppReduced": "{{moveName}} von {{targetName}} wird um {{reduction}} AP reduziert!", "mysteryEncounterAppeared": "Was ist das?", "battlerTagsHealBlock": "{{pokemonNameWithAffix}} kann nicht geheilt werden, da die Heilung blockiert wird!", "battlerTagsHealBlockOnRemove": "{{pokemonNameWithAffix}} kann wieder geheilt werden!", "pokemonTerastallized": "{{pokemonNameWithAffix}} terakristallisiert zum Typ {{type}}!"}