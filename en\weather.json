{"sunnyStartMessage": "The sunlight got bright!", "sunnyLapseMessage": "The sunlight is strong.", "sunnyClearMessage": "The sunlight faded.", "rainStartMessage": "A downpour started!", "rainLapseMessage": "The downpour continues.", "rainClearMessage": "The rain stopped.", "sandstormStartMessage": "A sandstorm brewed!", "sandstormLapseMessage": "The sandstorm rages.", "sandstormClearMessage": "The sandstorm subsided.", "sandstormDamageMessage": "{{pokemonNameWithAffix}} is buffeted\nby the sandstorm!", "hailStartMessage": "It started to hail!", "hailLapseMessage": "Hail continues to fall.", "hailClearMessage": "The hail stopped.", "hailDamageMessage": "{{pokemonNameWithAffix}} is pelted\nby the hail!", "snowStartMessage": "It started to snow!", "snowLapseMessage": "The snow is falling down.", "snowClearMessage": "The snow stopped.", "fogStartMessage": "A thick fog emerged!", "fogLapseMessage": "The fog continues.", "fogClearMessage": "The fog disappeared.", "heavyRainStartMessage": "A heavy rain began to fall!", "heavyRainLapseMessage": "The heavy downpour continues.", "heavyRainEffectMessage": "The Fire-type attack fizzled out in the heavy rain!", "heavyRainContinueMessage": "There is no relief from this heavy rain!", "heavyRainClearMessage": "The heavy rain has lifted!", "harshSunStartMessage": "The sunlight turned extremely harsh!", "harshSunLapseMessage": "The sunlight is scorching hot.", "harshSunEffectMessage": "The Water-type attack evaporated in the harsh sunlight!", "harshSunContinueMessage": "The extremely harsh sunlight was not lessened at all!", "harshSunClearMessage": "The harsh sunlight faded.", "strongWindsStartMessage": "Mysterious strong winds are protecting Flying-type Pokémon!", "strongWindsLapseMessage": "The strong winds blow intensely.", "strongWindsEffectMessage": "The mysterious strong winds weakened the attack!", "strongWindsContinueMessage": "The mysterious strong winds blow on regardless!", "strongWindsClearMessage": "The mysterious strong winds have dissipated!", "defaultEffectMessage": "The attack was blocked by the weather effect!"}