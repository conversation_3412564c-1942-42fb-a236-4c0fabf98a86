{"intro": "An unusual trainer with all kinds of Bug paraphernalia blocks your way!", "intro_dialogue": "Hey, trainer! I’m on a mission to find the rarest Bug Pokémon in existence!$You must love Bug Pokémon too, right?\nEveryone loves Bug Pokémon!", "title": "The Bug-Type Superfan", "speaker": "Bug-Type Superfan", "description": "The trainer prattles on, not even waiting for a response…\n\nIt seems the only way to get out of this situation is by catching the trainer’s attention!", "query": "What will you do?", "option": {"1": {"label": "Offer to Battle", "tooltip": "(-) Tough Battle\n(+) Teach any Pokémon a Bug-type Move", "selected": "A challenge, eh?\nMy bugs are more than ready for you!"}, "2": {"label": "Show Your Bug-types", "tooltip": "(+) Receive a Gift Item", "disabled_tooltip": "You need at least 1 Bug-type Pokémon on your team to select this.", "selected": "You show the trainer all your Bug-type Pokémon…", "selected_0_to_1": "Huh? You only have {{numBugTypes}}…$Guess I’m wasting my breath on someone like you…", "selected_2_to_3": "Hey, you’ve got {{numBugTypes}}!\nNot bad.$Here, this might help you on your journey to catch more!", "selected_4_to_5": "What? You have {{numBugTypes}}?\nNice!$You’re not quite at my level, but I can see shades of myself in you!$Take this, my young apprentice!", "selected_6": "Whoa! {{numBugTypes}}!$You must love Bug-types almost as much as I do!$Here, take this as a token of our camaraderie!"}, "3": {"label": "Gift a Bug Item", "tooltip": "(-) Give the trainer a {{requiredBugItems}}\n(+) Receive a Gift Item", "disabled_tooltip": "You need to have a {{requiredBugItems}} to select this.", "select_prompt": "Select an item to give.", "invalid_selection": "This Pokémon doesn’t have that kind of item.", "selected": "You hand the trainer a {{selectedItem}}.", "selected_dialogue": "Whoa! A {{selectedItem}}, for me?\nYou’re not so bad, kid!$As a token of my appreciation,\nI want you to have this special gift!$It’s been passed all through my family, and now I want you to have it!"}}, "battle_won": "Your knowledge and skill were perfect at exploiting our weaknesses!$In exchange for the valuable lesson,\nallow me to teach one of your Pokémon a Bug-type Move!", "teach_move_prompt": "Select a move to teach a Pokémon.", "confirm_no_teach": "You sure you don’t want to learn one of these great moves?", "outro": "I see great Bug Pokémon in your future!\nMay our paths cross again!$Bug out!", "numBugTypes_one": "{{count}} Bug-type", "numBugTypes_other": "{{count}} Bug-types"}