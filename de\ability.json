{"stench": {"name": "Duftnote", "description": "Lässt das Ziel beim Angriff eventuell durch Gestank zurückschrecken."}, "drizzle": {"name": "<PERSON><PERSON><PERSON>", "description": "Ruft bei Kampfantritt Regen herbei."}, "speedBoost": {"name": "Temposchub", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> in jeder Runde die Initiative."}, "battleArmor": {"name": "Kampfpanzer", "description": "Wehrt gegnerische Volltreffer mit einem harten Panzer ab."}, "sturdy": {"name": "Robustheit", "description": "Bietet Schutz gegen K.O.-Attacken. Bei vollen KP übersteht das Pokémon auch K.O.-Treffer."}, "damp": {"name": "Feuchtigkeit", "description": "Befeuchtet die Umgebung und verhindert so den Einsatz von Attacken wie Finale, die Explosionen auslösen."}, "limber": {"name": "Flexibilität", "description": "Der flexible Körper des Pokémon schützt es vor Paralyse."}, "sandVeil": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>h<PERSON><PERSON> in Sandstürmen den Ausweichwert."}, "static": {"name": "Statik", "description": "Kann bei Berührung durch statisch aufgeladenen Körper paralysieren."}, "voltAbsorb": {"name": "Voltabsorber", "description": "Treffer durch Elektro-Attacken verursachen keinen Schaden, sondern regenerieren stattdessen KP."}, "waterAbsorb": {"name": "H2O-Absorber", "description": "<PERSON><PERSON><PERSON> durch Wasser-Attacken verursachen keinen Schaden, sondern regenerieren stattdessen KP."}, "oblivious": {"name": "Dösigkeit", "description": "Das Pokémon ist so apathisch, dass es nicht betört oder provoziert werden kann."}, "cloudNine": {"name": "<PERSON><PERSON><PERSON> Si<PERSON>", "description": "Hebt alle Wetter-Effekte auf."}, "compoundEyes": {"name": "Facettenauge", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Genauigkeit von Attacken."}, "insomnia": {"name": "Insomnia", "description": "Verhindert Einschlafen."}, "colorChange": {"name": "Farbwechsel", "description": "<PERSON><PERSON><PERSON> seinen Typ zu dem der Attacke des Angreifers."}, "immunity": {"name": "Immunität", "description": "Das starke Immunsystem des Pokémon verhindert Vergiftungen."}, "flashFire": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Verstärkt Feuer-Attacken, wenn es von <PERSON>-Attacken getroffen wird."}, "shieldDust": {"name": "Puderabwehr", "description": "Blockiert durch Puder die Zusatzeffekte gegnerischer Angriffe."}, "ownTempo": {"name": "Tempomacher", "description": "Das Pokémon lässt sich nicht aus der Ruhe bringen und verhindert so Verwirrung."}, "suctionCups": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>t Attacken und Items, die Pokémon austauschen, indem es sich mit einem Saugnapf am Boden verankert."}, "intimidate": {"name": "<PERSON><PERSON><PERSON>", "description": "Senkt den Angriff der Gegner, indem es sie gleich zu Kampfantritt bedroht und einschüchtert."}, "shadowTag": {"name": "Wegsperre", "description": "Hindert Gegner an der Flucht beziehungsweise am Auswechseln, indem es ihnen den Weg versperrt."}, "roughSkin": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> werden durch die raue Haut des Pokémon bei direkten Attacken verletzt."}, "wonderGuard": {"name": "Wunderwache", "description": "<PERSON>ndersame Kräfte bewirken, dass nur sehr effektive Treffer bei ihm Schaden anrichten."}, "levitate": {"name": "Schwebe", "description": "Verleiht volle Immunität gegen alle Boden-Attacken durch Schwebezustand."}, "effectSpore": {"name": "Sporenwirt", "description": "Wird dieses Pokémon durch eine direkte Attacke angegriffen, kann das beim Gegner Paralyse, Vergiftung oder Schlaf auslösen."}, "synchronize": {"name": "Synchro", "description": "Erleidet das Pokémon Verbrennungen, Vergiftungen oder Paralyse, ereilt das jeweilige Statusproblem auch den Verursacher."}, "clearBody": {"name": "Neutraltorso", "description": "Verhindert das Senken der Statuswerte durch Attacken und Fähigkeiten von <PERSON>n."}, "naturalCure": {"name": "Innere Kraft", "description": "Wird das Pokémon ausgewechselt, werden seine Statusprobleme geheilt."}, "lightningRod": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Zieht Elektro-Attacken an. Statt durch diese Schaden zu nehmen, erhöht es den eigenen Spezial-Angriff."}, "sereneGrace": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Durch den Edelmut des Pokémon steigt die Wahrscheinlichkeit, dass Zusatzeffekte seiner Attacken auftreten. Interagiert nicht mit Items."}, "swiftSwim": {"name": "Wassertempo", "description": "Erhöht bei Regen die Initiative."}, "chlorophyll": {"name": "Chlorophyll", "description": "Erhöht bei Sonnenschein die Initiative."}, "illuminate": {"name": "Erleuchtung", "description": "Er<PERSON>t die Umgebung und erhöht dadurch die Wahrscheinlichkeit, von Doppelkämpfen mit wilden Pokémon. Verhindert Senkung der Genauigkeit des Pokémons."}, "trace": {"name": "Erfassen", "description": "Kopiert bei Kampfantritt die Fähigkeit eines Gegners."}, "hugePower": {"name": "Kraftkoloss", "description": "Verdoppelt die Stärke von physischen Attacken."}, "poisonPoint": {"name": "Giftdorn", "description": "Vergiftet den Angreifer bei Berührung eventuell."}, "innerFocus": {"name": "Konzentrator", "description": "Verhindert durch erhöhte Konzentrationsfähigkeit Zurückschrecken."}, "magmaArmor": {"name": "Magmapanzer", "description": "Dan<PERSON> eines Panzers aus Magma kann dieses Pokémon nicht eingefroren werden."}, "waterVeil": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Verhindert durch eine Hülle aus Wasser Verbrennungen."}, "magnetPull": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>-Pokémon durch Magnetismus an der Flucht."}, "soundproof": {"name": "Lärmschutz", "description": "Bietet durch Schalldämmung volle Immunität gegen alle Lärm-Attacken."}, "rainDish": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Regeneriert bei Regen nach und nach KP."}, "sandStream": {"name": "Sandsturm", "description": "Erzeugt bei Kampfantritt Sandstürme."}, "pressure": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>, be<PERSON> Ein<PERSON>z von Attacken mehr AP zu verbrauchen."}, "thickFat": {"name": "Speckschicht", "description": "Das Pokémon wird von einer dicken Fettschicht geschützt, was den durch Feuer- und Eis-Attacken erlittenen Schaden halbiert."}, "earlyBird": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Wenn es eingeschlafen ist, kann es doppelt so schnell wieder aufwachen wie andere Pokémon."}, "flameBody": {"name": "Flammkörper", "description": "<PERSON>ügt dem Angreifer bei Berührung eventuell Verbrennungen zu."}, "runAway": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Die Flucht vor wilden Pokémon gelingt immer."}, "keenEye": {"name": "Adlerauge", "description": "<PERSON>in s<PERSON><PERSON><PERSON> hindert <PERSON>, seine Genauigkeit zu senken."}, "hyperCutter": {"name": "Sc<PERSON>nma<PERSON>", "description": "<PERSON><PERSON>t Angreifer durch mächtige Scheren daran, den Angriffs-<PERSON>rt zu senken."}, "pickup": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> gelege<PERSON><PERSON> von Gegnern benutzte Items auf. Dies geschieht nicht nur während Kämpfen, sondern auch unterwegs."}, "truant": {"name": "Schnarchnase", "description": "Das Pokémon muss nach Einsatz einer Attacke eine Runde lang aussetzen."}, "hustle": {"name": "Übereifer", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> den Angriffs-Wert, aber senkt die Genauigkeit."}, "cuteCharm": {"name": "Charmebolzen", "description": "Wird dieses Pokémon durch eine direkte Attacke angegriffen, verliebt sich der Gegner eventuell in es."}, "plus": {"name": "Plus", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> den Spezial-Angriff, wenn das Pokémon einen Mitstreiter mit der Fähigkeit Plus oder Minus hat."}, "minus": {"name": "Minus", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> den Spezial-Angriff, wenn das Pokémon einen Mitstreiter mit der Fähigkeit Plus oder Minus hat."}, "forecast": {"name": "Prognose", "description": "<PERSON>mmt je nach Wetter entweder den Typ Wasser, <PERSON>uer oder E<PERSON> an."}, "stickyHold": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Trägt es ein Item, ble<PERSON>t dieses an seinem klebrigen Körper haften, wodurch Item-Diebstahl verhindert wird."}, "shedSkin": {"name": "Expidermis", "description": "Das Pokémon befreit sich eventuel<PERSON> von Statusproblemen, indem es seine Haut abstreift."}, "guts": {"name": "Adrenalin", "description": "Bei Statusproblemen setzt es Adrenalin frei und erhöht so seinen Angriffs-Wert."}, "marvelScale": {"name": "Notschutz", "description": "Bei Statusproblemen schützt es sich mit mysteriösen Schuppen und erhöht so seine Verteidigung."}, "liquidOoze": {"name": "Kloakensoße", "description": "<PERSON><PERSON><PERSON>, die durch Saug-Attacken Kloakensoße in sich aufgenommen haben, nehmen durch deren widerwärtigen Gestank Schaden."}, "overgrow": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke von Pflanzen-Attacken, wenn die KP auf einen gewissen Wert fallen."}, "blaze": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke von Feuer-Attacken, wenn die KP auf einen gewissen Wert fallen."}, "torrent": {"name": "Sturzbach", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke von Wasser-Attacken, wenn die KP auf einen gewissen Wert fallen."}, "swarm": {"name": "Hexaplaga", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke von Käfer-Attacken, wenn die KP auf einen gewissen Wert fallen."}, "rockHead": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Verhin<PERSON>t Schaden, der durch Rückstoß entstehen würde."}, "drought": {"name": "<PERSON><PERSON><PERSON>", "description": "Erzeugt bei Kampfantritt gleißendes Sonnenlicht."}, "arenaTrap": {"name": "Ausweglos", "description": "Das Pokémon hindert <PERSON> an der Flucht beziehungsweise am Auswechseln. E<PERSON><PERSON><PERSON><PERSON> die Chance auf Doppelkämpfe."}, "vitalSpirit": {"name": "Munterkeit", "description": "Das Pokémon ist so munter, dass es nicht einschlafen kann."}, "whiteSmoke": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Indem es sich mit pulvrigem <PERSON>, hindert es <PERSON>, seine Status<PERSON> zu senken."}, "purePower": {"name": "Mentalkraft", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> mit reiner Willenskraft die Stärke seiner physischen Attacken."}, "shellArmor": {"name": "<PERSON><PERSON><PERSON>", "description": "Wehrt gegnerische Volltreffer mit einem harten Panzer ab."}, "airLock": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Hebt alle Wetter-Effekte auf."}, "tangledFeet": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> den Ausweichwert, wenn das Pokémon verwirrt ist."}, "motorDrive": {"name": "<PERSON><PERSON><PERSON>", "description": "Treffer durch Elektro-Attacken verursachen keinen Schaden, sondern geben dem <PERSON> eine Starthilfe und erhöhen so seine Initiative."}, "rivalry": {"name": "Rivalität", "description": "Greift es einen Rivalen desselben Geschlechts an, wird es stärker. Greift es ein Ziel des anderen Geschlechts an, wird es schwächer."}, "steadfast": {"name": "Felsenfest", "description": "Sein eiserner Wille erhöht die Initiative, wann immer das Pokémon zurückschreckt."}, "snowCloak": {"name": "Schneemantel", "description": "Erhöht bei Hagel den Ausweichwert."}, "gluttony": {"name": "Völlerei", "description": "Setzt bestimmte Beeren nicht erst in einer Notlage ein, sondern bereits dann, wenn seine KP auf die Hälfte des Maximalwerts fallen."}, "angerPoint": {"name": "Kurzsch<PERSON><PERSON>", "description": "Wird nach Einstecken eines Volltreffers wütend und maximiert dabei seinen Angriffs-Wert."}, "unburden": {"name": "Entlastung", "description": "<PERSON><PERSON> das von ihm getragene Item verwendet wird oder verloren geht, erhöht dies seine Initiative."}, "heatproof": {"name": "Hitzeschutz", "description": "Sein Hitze abweisender Körper halbiert den durch Feuer-Attacken erlittenen Schaden."}, "simple": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Verdoppelt die Wirkung eigener Statusveränderungen."}, "drySkin": {"name": "Trockenheit", "description": "Bei Sonnenschein verliert das Pokémon KP und der Schaden durch Feuer-Attacken steigt. Bei Regen und Treffern durch Wasser-Attacken regeneriert es KP."}, "download": {"name": "Download", "description": "Ist die Spezial-Verteidigung des Gegners höher als seine Verteidigung, wird der eigene Spezial-Angriff erhöht. Ist die Verteidigung höher, steigt der Angriff."}, "ironFist": {"name": "Eisenfaust", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke von <PERSON>, Punch-, Faust- und Schlag-Attacken."}, "poisonHeal": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Das Pokémon erleidet keinen Schaden durch Vergiftung, sondern regeneriert KP."}, "adaptability": {"name": "Anpassung", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke von Attacken, die dem Typ des Pokémon entsprechen."}, "skillLink": {"name": "Wertelink", "description": "Landet mit Serien-Attacken immer die maximale Anzahl an Treffern."}, "hydration": {"name": "Hydration", "description": "Heilt bei Regen Statusprobleme."}, "solarPower": {"name": "Solarkraft", "description": "Führt bei Sonnenschein in jeder Runde zu KP-Verlusten, erhöht aber den Spezial-Angriff."}, "quickFeet": {"name": "<PERSON><PERSON><PERSON>", "description": "Erhöht bei Statusproblemen die Initiative."}, "normalize": {"name": "Regulierung", "description": "Alle Attacken des Pokémon nehmen den Typ Normal an und ihre Stärke erhöht sich ein wenig."}, "sniper": {"name": "Superschütze", "description": "<PERSON>rhöht bei Volltreffern die Stärke der Attacke noch weiter."}, "magicGuard": {"name": "<PERSON><PERSON><PERSON>", "description": "Das Pokémon nimmt nur durch Offensiv-Attacken Schaden."}, "noGuard": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Alle Attacken des oder auf das Pokémon gelingen aufgrund seiner deckungslosen Kampftaktik. Erhö<PERSON> die Chance auf Doppelkämpfe."}, "stall": {"name": "Zeitspiel", "description": "Handelt auch mit Initiative-Vorteil stets als Letztes."}, "technician": {"name": "Techniker", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke von schwächeren Attacken."}, "leafGuard": {"name": "Floraschild", "description": "Verhindert bei Sonnenschein Statusprobleme."}, "klutz": {"name": "<PERSON><PERSON><PERSON>tsch", "description": "Das Pokémon kann keine getragenen Items verwenden."}, "moldBreaker": {"name": "Überbrückung", "description": "Attacken können ungeachtet der Fähigkeiten des Zieles verwendet werden."}, "superLuck": {"name": "Glückspilz", "description": "Großes Glück erhöht die Wahrscheinlichkeit, einen Volltreffer zu landen."}, "aftermath": {"name": "Finalschlag", "description": "Wird das Pokémon durch eine direkte Attacke besiegt, fügt es dem Angreifer Sc<PERSON>en zu."}, "anticipation": {"name": "Vorahnung", "description": "Kann gefährliche gegnerische Attacken erahnen."}, "forewarn": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Gibt bei Kampfantritt Auskunft über eine Attacke aus dem gegnerischen Repertoire."}, "unaware": {"name": "Unkenntnis", "description": "Das Pokémon ignoriert beim Angriff sämtliche Statusveränderungen des Zieles."}, "tintedLens": {"name": "Aufwertung", "description": "<PERSON><PERSON><PERSON><PERSON>, dass nicht sehr effektive Attacken dem Ziel trotz des Typennachteils normalen Schaden zufügen."}, "filter": {"name": "Filter", "description": "Reduziert die Stärke von sehr effektiven Attacken und verringert damit den Schaden, den das Pokémon durch sie erle<PERSON>t."}, "slowStart": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Halbiert fünf Runden lang den Angriffs-Wert und die Initiative des Pokémon."}, "scrappy": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>, dass Normal- und Kampf-Attacken auch Pokémon vom Typ Geist treffen können."}, "stormDrain": {"name": "Sturmsog", "description": "<PERSON><PERSON><PERSON>-Attacken an. Statt durch diese Schaden zu nehmen, erhöht es den eigenen Spezial-Angriff."}, "iceBody": {"name": "<PERSON><PERSON><PERSON>", "description": "Regeneriert bei Hagel nach und nach KP."}, "solidRock": {"name": "Felskern", "description": "Reduziert die Stärke von sehr effektiven Attacken und verringert damit den Schaden, den das Pokémon durch sie erle<PERSON>t."}, "snowWarning": {"name": "Hagelalarm", "description": "<PERSON>öst bei Kampfantritt Hagel aus."}, "honeyGather": {"name": "Honigmaul", "description": "Sammelt nach dem Kampf Honig. Dieser wird dann für Geld verkauft."}, "frisk": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Kann bei Kampfantritt Auskunft über die Fähigkeit vom Gegner geben."}, "reckless": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke von Attacken mit Rückstoßschaden."}, "multitype": {"name": "Variabilität", "description": "Das Pokémon passt seinen Typ dem der getragenen Tafel an."}, "flowerGift": {"name": "Pflanzengabe", "description": "Erhöht bei Sonnenschein den Angriff und die Spezial-Verteidigung aller Team-Pokémon."}, "badDreams": {"name": "Alptraum", "description": "<PERSON><PERSON><PERSON> schlafenden Gegnern Schaden zu."}, "pickpocket": {"name": "<PERSON><PERSON>", "description": "Stiehlt das Item des Angreifers bei Berührung."}, "sheerForce": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke von Attacken, aber hebt dafür ihre Zusatzeffekte auf."}, "contrary": {"name": "Umkehrung", "description": "Statusveränderungen werden umgekehrt: Statuswerte, die eigentlich erhöht werden sollten, sinken und umgekehrt."}, "unnerve": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>rz<PERSON>gt bei Gegnern Stress und hindert sie so daran, <PERSON><PERSON> zu konsumieren."}, "defiant": {"name": "<PERSON>swille", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> den Angriff stark, wenn ein <PERSON>wert gesenkt wurde."}, "defeatist": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Fallen seine KP auf die Hälfte des Maximalwerts oder weniger, bekommt es Angst. Dadurch wird die Stärke seines Angriffs und Spezial-Angriffs halbiert."}, "cursedBody": {"name": "Tastfluch", "description": "<PERSON><PERSON>t eventuell die Attacke, mit welcher der Angreifer es getroffen hat."}, "healer": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Befreit Mitstreiter gelegentlich von Statusproblemen."}, "friendGuard": {"name": "Freundeshut", "description": "<PERSON><PERSON> <PERSON>, den Mitstreiter er<PERSON>iden, verring<PERSON>."}, "weakArmor": {"name": "Bruchrüstung", "description": "Senkt bei erlittenem Treffer durch eine physische Attacke die Verteidigung des Pokémon, aber erhöht dafür seine Initiative stark."}, "heavyMetal": {"name": "Schwermetall", "description": "Verdoppelt das eigene Gewicht."}, "lightMetal": {"name": "Leichtmetall", "description": "Halbiert das eigene Gewicht."}, "multiscale": {"name": "Multischuppe", "description": "Verringert den erlittenen Schaden bei vollen KP."}, "toxicBoost": {"name": "<PERSON><PERSON><PERSON>", "description": "Erhöht bei Vergiftungen die Stärke von physischen Attacken."}, "flareBoost": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Erhöht bei Verbrennungen die Stärke von Spezial-Attacken."}, "harvest": {"name": "Reiche Ernte", "description": "Dieselbe Beere kann mehrmals verwendet werden."}, "telepathy": {"name": "Telepathie", "description": "<PERSON><PERSON><PERSON><PERSON> und pariert <PERSON> von <PERSON>."}, "moody": {"name": "Gefühlswippe", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> in jeder Runde aufs Neue einen Statuswert stark und senkt einen anderen."}, "overcoat": {"name": "Partikelschutz", "description": "Nimmt weder durch Wetterlagen wie Sandsturm oder Hagel noch durch Pulver oder Puder Schaden."}, "poisonTouch": {"name": "Giftgriff", "description": "Kann das Ziel durch bloßes Berühren vergiften."}, "regenerator": {"name": "Belebekraft", "description": "Wird das Pokémon ausgewechselt, regeneriert es eine kleine Menge an KP."}, "bigPecks": {"name": "Brustbieter", "description": "<PERSON><PERSON><PERSON>, die Verteidigung des Pokémon zu senken."}, "sandRush": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> in Sandstürmen die Initiative."}, "wonderSkin": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Wehrt mit robustem Körper viele Status-Attacken ab."}, "analytic": {"name": "Analyse", "description": "Gre<PERSON> das Pokémon zuletzt an, erhöht sich die Stärke der Attacke, die es einsetzt."}, "illusion": {"name": "Trugbild", "description": "Führt den Gegner hinters Licht, indem es bei Kampfantritt die Gestalt des Pokémon an der letzten Stelle im Team annimmt."}, "imposter": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Kämpft als Kopie seines Gegenübers."}, "infiltrator": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Überwindet gegnerische Schilde sowie Delegatoren und greift an."}, "mummy": {"name": "<PERSON><PERSON>", "description": "Überträgt bei Berührung die Fähigkeit Mumie auf den Angreifer."}, "moxie": {"name": "Hochmut", "description": "Besiegt es ein Pokémon, steigt sein Selbstvertrauen und somit auch sein Angriff."}, "justified": {"name": "Redlichkeit", "description": "Wird es von einer Unlicht-Attacke getroffen, meldet sich sein Sinn für Gerechtigkeit zu Wort und sein Angriff steigt."}, "rattled": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Wird es von einer Unlicht-, Geister- oder Käfer-Attacke getroffen oder bedroht, bekommt es Angst und seine Initiative steigt."}, "magicBounce": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Lenkt Status-Attacken auf den Angreifer um, ohne selbst von ihnen getroffen zu werden."}, "sapSipper": {"name": "Vegetarier", "description": "Wird es von einer Pflanzen-Attacke getroffen, erleidet es keinerlei Schaden und sein Angriff steigt."}, "prankster": {"name": "Strolch", "description": "Ermöglicht einen Erstschlag mit Status-Attacken."}, "sandForce": {"name": "Sandgewalt", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> in Sandstürmen die Stärke von Gesteins-, Boden- und Stahl-Attacken."}, "ironBarbs": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>ügt dem Angreifer bei Berührung mit eisernen Stacheln Schaden zu."}, "zenMode": {"name": "Trance-Modus", "description": "Fallen seine KP auf die Hälfte des Maximalwerts oder weniger, wechselt es seine Gestalt."}, "victoryStar": {"name": "Triumphstern", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Genauigkeit aller Team-Pokémon."}, "turboblaze": {"name": "Turbobrand", "description": "Attacken können ungeachtet der Fähigkeit des Zieles eingesetzt werden."}, "teravolt": {"name": "Teravolt", "description": "Attacken können ungeachtet der Fähigkeit des Zieles eingesetzt werden."}, "aromaVeil": {"name": "Dufthülle", "description": "<PERSON>nn alle Team-Pokémon vor mentalen Angriffen schützen."}, "flowerVeil": {"name": "Blütenhülle", "description": "Schützt Mitstreiter vom Typ Pflanze vor dem Senken ihrer Statuswerte sowie vor Statusproblemen."}, "cheekPouch": {"name": "Backentaschen", "description": "Regeneriert beim Konsum von Beeren ungeachtet der Beerensorte KP."}, "protean": {"name": "Wandlungskunst", "description": "Das Pokémon nimmt bei Einsatz einer Attacke deren Typ an."}, "furCoat": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> den Schaden, den das Pokémon durch physische Attacken erleidet."}, "magician": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>t das Pokémon ein Ziel mit einer Attacke, kann es ihm dabei sein Item stehlen."}, "bulletproof": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>nn das <PERSON> vor geworfenen kugelförmigen Objekten, wie zum Beispiel Bomben, sch<PERSON><PERSON><PERSON>."}, "competitive": {"name": "Unbeugsamkeit", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> den Spezial-<PERSON><PERSON> stark, wenn ein <PERSON>t gesenkt wurde."}, "strongJaw": {"name": "<PERSON><PERSON><PERSON>", "description": "Der kräftige Kiefer des Pokémon erhöht die Stärke von Biss-Attacken."}, "refrigerate": {"name": "Frostschicht", "description": "Attacken vom Typ Normal nehmen den Typ Eis an und ihre Stärke erhöht sich ein wenig."}, "sweetVeil": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Alle Team-Pokémon können nicht einschlafen."}, "stanceChange": {"name": "Taktikwechsel", "description": "Setzt das Pokémon eine Offensiv-Attacke ein, nimmt es die Klingenform an. Setzt es danach die Attacke Königsschild ein, nimmt es die Schildform an."}, "galeWings": {"name": "Orkanschwingen", "description": "Kann bei vollen KP einen Erstschlag mit Flug-Attacken ermöglichen."}, "megaLauncher": {"name": "Megawumme", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke einiger Wellen-, Aura- und Puls-Attacken."}, "grassPelt": {"name": "Pflanzenpelz", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Verteidigung, wenn Grasfeld aktiv ist."}, "symbiosis": {"name": "Nutznießer", "description": "<PERSON><PERSON><PERSON>, die ihr Item aufgebraucht haben, sein eigenes Item."}, "toughClaws": {"name": "Krallenwucht", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke von direkten Attacken."}, "pixilate": {"name": "Feenschicht", "description": "Attacken vom Typ Normal nehmen den Typ Fee an und ihre Stärke erhöht sich ein wenig."}, "gooey": {"name": "Viskosität", "description": "Senkt bei Berührung im Zuge eines Angriffs die Initiative des Angreifers."}, "aerilate": {"name": "<PERSON><PERSON><PERSON>", "description": "Attacken vom Typ Normal nehmen den Typ Flug an und ihre Stärke erhöht sich ein wenig."}, "parentalBond": {"name": "Familienbande", "description": "Zwei Generationen setzen jeweils ein Mal zum Angriff an."}, "darkAura": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke aller Attacken des Typs Unlicht."}, "fairyAura": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke aller Attacken des Typs Fee."}, "auraBreak": {"name": "Aura-Umkehr", "description": "Kehrt die Wirkung von Auren um und senkt so die Stärke bestimmter Attacken, anstatt sie zu erhöhen."}, "primordialSea": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> das Wetter, um Feuer-Attacken wirkungslos zu machen."}, "desolateLand": {"name": "Endland", "description": "<PERSON><PERSON><PERSON> das Wetter, um Wasser-Attacken wirkungslos zu machen."}, "deltaStream": {"name": "Delta-Wind", "description": "<PERSON><PERSON><PERSON> das Wetter, um die Schwächen des Typs Flug zu beseitigen."}, "stamina": {"name": "Zähigkeit", "description": "Wird es von einer Attacke getroffen, steigt seine Verteidigung."}, "wimpOut": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Fallen seine KP auf die Hälfte des Maximalwerts oder weniger, zieht es sich ängstlich zurück."}, "emergencyExit": {"name": "Rückzug", "description": "Fallen seine KP auf die Hälfte des Maximalwerts oder weniger, bringt es sich in Sicherheit."}, "waterCompaction": {"name": "Verklumpen", "description": "Wird es von einer Wasser-Attacke getroffen, steigt seine Verteidigung stark."}, "merciless": {"name": "Q<PERSON><PERSON><PERSON><PERSON>", "description": "Sorgt bei Angriffen auf vergiftete Ziele für Volltreffergarantie."}, "shieldsDown": {"name": "Limitschild", "description": "Fallen seine KP auf die Hälfte des Maximalwerts oder weniger, zerbricht die Panzerung des Pokémon und es wird aggressiver."}, "stakeout": {"name": "Beschattung", "description": "Bewirkt bei Angriffen auf neu eingewechselte Ziele doppelten Schaden."}, "waterBubble": {"name": "Wasserblase", "description": "Feuer-Attacken fügen dem Pokémon weniger Schaden zu. Verhindert Verbrennungen."}, "steelworker": {"name": "Stahlprofi", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke von Stahl-Attacken."}, "berserk": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Fallen seine KP nach einem Angriff auf die Hälfte des Maximalwerts oder weniger, steigt sein Spezial-Angriff."}, "slushRush": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Erhöht bei Hagel die Initiative."}, "longReach": {"name": "Langstreck<PERSON>", "description": "Ermöglicht dem Pokémon den Einsatz aller seiner Attacken, ohne das Ziel dabei direkt zu berühren."}, "liquidVoice": {"name": "Plätscherstimme", "description": "<PERSON><PERSON><PERSON><PERSON>, dass alle Lärm-Attacken des Pokémon den Typ Wasser annehmen."}, "triage": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Ermöglicht einen Erstschlag mit Attacken, welche die KP des Anwenders direkt regenerieren."}, "galvanize": {"name": "Elektrohaut", "description": "Attacken vom Typ Normal nehmen den Typ Elektro an und ihre Stärke erhöht sich ein wenig."}, "surgeSurfer": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Verdoppelt die Initiative, wenn zuvor ein Elektrofeld erzeugt wurde."}, "schooling": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Verfügt es über einen hohen KP-Wert, wird es zu einem Schwarm und gewinnt an Stärke. Ist der KP-Wert niedrig, löst sich der Schwarm wieder auf."}, "disguise": {"name": "Kostümspuk", "description": "Kann ein Mal pro Kampf mit seinem gruseligen Kostüm einen Angriff abwehren."}, "battleBond": {"name": "Freundschaftsakt", "description": "Besiegt es ein Ziel, vertieft dies die Freundschaft zu seinem Trainer, wodurch es die Ash-Form annimmt und sein Was<PERSON>-Shuriken stärker wird."}, "powerConstruct": {"name": "Sc<PERSON><PERSON><PERSON>", "description": "Fallen seine KP auf die Hälfte des Maximalwerts oder weniger, eilen ihm weitere Zellen zu Hilfe und es nimmt die Optimumform an."}, "corrosion": {"name": "Korrosion", "description": "<PERSON>nn selbst Pokémon vom Typ Stahl oder Gift vergiften."}, "comatose": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Das Pokémon befindet sich ununterbrochen im Halbschlaf und wacht nie vollständig auf. Es kann jedoch im Schlaf angreifen. Es gilt als von einer Statusveränderung betroffen."}, "queenlyMajesty": {"name": "Majestät", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> ein und hindert sie so daran, Erstschlag-Attacken gegen es einzusetzen."}, "innardsOut": {"name": "Magenkrempler", "description": "Wird es durch eine Attacke besiegt, fügt es dem Angreifer Schaden in Höhe des KP-Werts zu, den es besaß, bevor es kampfunfähig wurde."}, "dancer": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Kann direkt im Anschluss an die Tanz-Attacke eines anderen Pokémon ebenfalls eine solche einsetzen."}, "battery": {"name": "<PERSON><PERSON>ie", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke der Spezial-Attacken seiner Mitstreiter."}, "fluffy": {"name": "Flauschigkeit", "description": "Hal<PERSON><PERSON> den Schaden, den es durch direkte Attacken nimmt, aber verdoppelt dafür den durch Feuer-Attacken erlittenen Schaden."}, "dazzling": {"name": "Buntk<PERSON>rper", "description": "Überrascht Gegner und hindert sie so daran, Erstschlag-Attacken gegen es einzusetzen."}, "soulHeart": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>, wenn ein <PERSON> besiegt wird, den eigenen Spezial-Angriff."}, "tanglingHair": {"name": "<PERSON><PERSON><PERSON>", "description": "Senkt bei Berührung im Zuge eines Angriffs die Initiative des Angreifers."}, "receiver": {"name": "Receiver", "description": "Wird einer seiner Mitstreiter besiegt, erhält es dessen Fähigkeit."}, "powerOfAlchemy": {"name": "Chemiekraft", "description": "Wechselt seine Fähigkeit zu der eines kampfunfähig gewordenen Mitstreiters."}, "beastBoost": {"name": "Bestien-Boost", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> in jeder Runde, in der es ein anderes Pokémon besiegt, seinen höchsten Statuswert."}, "rksSystem": {"name": "Alpha-System", "description": "Das Pokémon passt seinen Typ der getragenen Disc an."}, "electricSurge": {"name": "Elektro-Erzeuger", "description": "Erzeugt bei Kampfantritt ein Elektrofeld."}, "psychicSurge": {"name": "Psycho-Erzeuger", "description": "<PERSON>rzeugt bei Kampfantritt ein Psychofeld."}, "mistySurge": {"name": "Nebel-<PERSON><PERSON><PERSON><PERSON>", "description": "Erzeugt bei Kampfantritt ein Nebelfeld."}, "grassySurge": {"name": "Gras-Erzeuger", "description": "<PERSON>rzeugt bei Kampfantritt ein Grasfeld."}, "fullMetalBody": {"name": "Metallprotektor", "description": "Verhindert das Senken der Statuswerte durch Attacken und Fähigkeiten von <PERSON>n."}, "shadowShield": {"name": "Phantomschutz", "description": "Verringert den erlittenen Schaden bei vollen KP."}, "prismArmor": {"name": "Prismarüstung", "description": "Reduziert die Stärke von sehr effektiven Attacken und verringert damit den Schaden, den das Pokémon durch sie erle<PERSON>t."}, "neuroforce": {"name": "Zerebralmacht", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke von sehr effektiven Attacken."}, "intrepidSword": {"name": "Kühn<PERSON> Schwert", "description": "Erhöht bei Kampfantritt den Angriff."}, "dauntlessShield": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> bei Kampfantritt die Verteidigung."}, "libero": {"name": "Libero", "description": "Das Pokémon nimmt bei Einsatz einer Attacke deren Typ an."}, "ballFetch": {"name": "Apport", "description": "Trägt das Pokémon kein <PERSON>em bei sich, hebt es den Ball aus dem ersten gescheiterten Fangversuch des Kampfes wieder auf."}, "cottonDown": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Wird es von einem Angriff getroffen, verstreut es Teile seines Wollflaums, wodurch die Initiative aller anderen Pokémon sinkt."}, "propellerTail": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Ignoriert die Effekte von Fähigkeiten und Attacken anderer Pokémon, die Attacken auf sich lenken."}, "mirrorArmor": {"name": "Spiegelrüstung", "description": "Lenkt ausschließlich Effekte, welche die Statuswerte des Pokémon senken würden, auf den Angreifer um."}, "gulpMissile": {"name": "Würggeschoss", "description": "<PERSON>n das Pokémon Surfer oder Taucher einsetzt, fängt es sich dabei Beute. Erleidet es anschließend Schaden, greift es an, indem es die Beute wieder ausspuckt."}, "stalwart": {"name": "Stahlrück<PERSON>t", "description": "Ignoriert die Effekte von Fähigkeiten und Attacken anderer Pokémon, die Attacken auf sich lenken."}, "steamEngine": {"name": "Dampfant<PERSON><PERSON>", "description": "Wird es von einer Wasser- oder Feuer-Attacke getroffen, steigt seine Initiative drastisch."}, "punkRock": {"name": "Punk Rock", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke von eigenen Lärm-Attacken und halbiert den Schaden, den das Pokémon selbst durch Lärm-Attacken erleidet."}, "sandSpit": {"name": "Sandspeier", "description": "<PERSON><PERSON><PERSON> einen Sandsturm aus, wenn das Pokémon von einer Attacke erfasst wird."}, "iceScales": {"name": "Eisflügelstaub", "description": "Halbiert mithil<PERSON> von schützendem Eisflügelstaub den Schaden, den das Pokémon durch Spezial-Attacken erleidet."}, "ripen": {"name": "Heranreifen", "description": "Verdoppelt den Effekt von Beeren, indem es sie heranreifen lässt."}, "iceFace": {"name": "Tiefkühlkopf", "description": "Der Eisblock um seinen Kopf blockt eine physische Attacke ab. Dies bewirkt jedoch einen Formwechsel. Durch Hagel wird der Eisblock wiederhergestellt."}, "powerSpot": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>h<PERSON><PERSON> bei direkt benachbarten Pokémon die Stärke von Attacken."}, "mimicry": {"name": "<PERSON><PERSON><PERSON>", "description": "Der Typ des Pokémon ändert sich in Abhängigkeit vom Zustand des Feldes."}, "screenCleaner": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Hebt bei Kampfantritt die Wirkung von Lichtschild, Reflektor und Auroraschleier auf Mitstreiter- und Gegnerseite auf."}, "steelySpirit": {"name": "Stählerner Wille", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke von Stahl-Attacken auf Mitstreiterseite."}, "perishBody": {"name": "Unheilskörper", "description": "Erleidet es einen Treffer von einer direkten Attacke, wird es zusammen mit dem Angreifer nach drei Runden besiegt. Rettung ist durch Austausch möglich."}, "wanderingSpirit": {"name": "<PERSON><PERSON><PERSON>", "description": "Wird das <PERSON> von einer direkten Attacke getroffen, tauscht es seine Fähigkeit mit der des Angreifers."}, "gorillaTactics": {"name": "Affenfokus", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> den Angriff, aber nur die zuerst gewählte Attacke kann eingesetzt werden."}, "neutralizingGas": {"name": "Reaktionsgas", "description": "Solange ein Pokémon mit der Fähigkeit Reaktionsgas am Kampf beteiligt ist, werden die Fähigkeiten aller anderen Pokémon, ausgenommen dem Nutzer, unterdrückt oder aufgehoben."}, "pastelVeil": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Schützt das Pokémon und seine Mitstreiter vor Vergiftung."}, "hungerSwitch": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Das Pokémon ändert zum Ende jeder Runde seine Form und wechselt somit zwischen dem Pappsatt- und dem Kohldampfmuster."}, "quickDraw": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Ermöglicht dem Pokémon gelegentlich den Erstschlag."}, "unseenFist": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> das Pokémon eine direkte Attacke einsetzt, trifft diese auch dann, wenn sich das Ziel selbst schützt."}, "curiousMedicine": {"name": "<PERSON><PERSON><PERSON>", "description": "Das Pokémon versprüht bei Kampfantritt Arznei aus seiner Muschel, die alle Statusveränderungen auf der Mitstreiterseite aufhebt."}, "transistor": {"name": "Transistor", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke von Elektro-Attacken."}, "dragonsMaw": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Stärke von Drachen-Attacken."}, "chillingNeigh": {"name": "<PERSON><PERSON>", "description": "Besiegt es ein Pokémon, stößt es ein frostiges Wiehern aus und erhöht damit seinen Angriff."}, "grimNeigh": {"name": "<PERSON><PERSON><PERSON>", "description": "Besiegt es ein Pokémon, stößt es ein furchteinflößendes Wiehern aus und erhöht damit seinen Spezial-Angriff."}, "asOneGlastrier": {"name": "Reitgespann", "description": "Das Pokémon verfügt sowohl über Coronospas Fähigkeit Anspannung als auch über Polaross’ Fähigkeit Helles Wiehern."}, "asOneSpectrier": {"name": "Reitgespann", "description": "Das Pokémon verfügt sowohl über Coronospas Fähigkeit Anspannung als auch über Phantoross’ Fähigkeit Dunkles Wiehern."}, "lingeringAroma": {"name": "Duftschwade", "description": "Das Pokémon überträgt bei Berührung die Fähigkeit Duftschwade auf den Angreifer."}, "seedSower": {"name": "Streusaat", "description": "Wird das Pokémon von einem Angriff getroffen, erzeugt es ein Grasfeld."}, "thermalExchange": {"name": "Thermowandel", "description": "Wird das <PERSON> von einer Feuer-<PERSON><PERSON> getroffen, steigt sein <PERSON>. Außerdem kann es keine Verbrennung erleiden."}, "angerShell": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Fallen die KP des Pokémon durch einen Angriff auf die Hälfte des Maximalwerts oder weniger, sinken Vert. und Sp.-Vert., aber Ang., Sp.-Ang. und Initiative steigen."}, "purifyingSalt": {"name": "Läutersalz", "description": "Das Pokémon kann dank seines läuternden Salzes keine Statusprobleme erleiden und der durch Geist-Attacken erlittene Schaden wird halbiert."}, "wellBakedBody": {"name": "Knusperkruste", "description": "Wird das Pokémon von einer Feuer-Attacke getroffen, erleidet es keinen Schaden. Stattdessen steigt seine Verteidigung stark."}, "windRider": {"name": "Windreiter", "description": "<PERSON><PERSON><PERSON> Rückenwind oder wird das Pokémon von einer Wind-Attacke getroffen, steigt sein Ang<PERSON>. Außerdem erleidet es keinen Schaden durch Wind-Attacken."}, "guardDog": {"name": "Wachhund", "description": "Wird das Pokémon bedroht, steigt sein Angriff. Attacken und Items, durch die Pokémon ausgetauscht werden, haben keine Wirkung auf es."}, "rockyPayload": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Die Stärke von Gesteins-Attacken des Pokémon steigt."}, "windPower": {"name": "Windkraft", "description": "Wird das Pokémon von einer Wind-Attacke getroffen, lädt es sich auf. <PERSON><PERSON><PERSON> steigt die Stärke seiner nächsten Elektro-Attacke."}, "zeroToHero": {"name": "Superwechsel", "description": "Wird das Pokémon ausgewechselt, nimmt es die Heldenform an."}, "commander": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Befindet sich ein Heerashai auf der Mitstreiterseite, springt das Pokémon bei Kampfantritt in dessen Maul und gibt von dort aus Befehle. E<PERSON><PERSON><PERSON><PERSON> die Chance auf Doppelkämpfe."}, "electromorphosis": {"name": "Dynamo", "description": "<PERSON><PERSON> das Pokémon Schaden erle<PERSON>t, lädt es sich auf. <PERSON><PERSON><PERSON> steigt die Stärke seiner nächsten Elektro-Attacke."}, "protosynthesis": {"name": "Paläosynthese", "description": "<PERSON><PERSON> Sonnenschein oder wenn das Pokémon eine Energiekapsel trägt, steigt sein höchs<PERSON> Status<PERSON>."}, "quarkDrive": {"name": "Quantenantrieb", "description": "<PERSON><PERSON> Elektrofeld oder wenn das Pokémon eine Energiekapsel trägt, steigt sein höchs<PERSON> Status<PERSON>."}, "goodAsGold": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Dank seines robusten Körpers aus reinem, rostfreiem Gold kann das Pokémon nicht von Status-Attacken getroffen werden."}, "vesselOfRuin": {"name": "Unheilsgefäß", "description": "<PERSON><PERSON> der Macht seines Unheil bringenden Gefäßes schwächt das Pokémon den Spezial-Angriff aller anderen Pokémon."}, "swordOfRuin": {"name": "Unheilsschwert", "description": "<PERSON><PERSON> der Macht seines Unheil bringenden Schwertes schwächt das Pokémon die Verteidigung aller anderen Pokémon."}, "tabletsOfRuin": {"name": "Unheilstafeln", "description": "<PERSON><PERSON> der <PERSON>ht seiner Unheil bringenden Holztafeln schwächt das Pokémon den Angriff aller anderen Pokémon."}, "beadsOfRuin": {"name": "Unheilsjuwelen", "description": "<PERSON><PERSON> der <PERSON>ht seiner Unheil bringenden Juwelen schwächt das Pokémon die Spezial-Verteidigung aller anderen Pokémon."}, "orichalcumPulse": {"name": "Orichalkum-Puls", "description": "Das Pokémon erzeugt bei Kampfantritt Sonnenschein. Bei Sonnenschein verstärkt ein urzeitlicher Puls seinen Angriff."}, "hadronEngine": {"name": "Hadronen-Motor", "description": "Das Pokémon erzeugt bei Kampfantritt ein Elektrofeld. Wenn ein Elektrofeld aktiv ist, verstärkt ein futuristischer Motor seinen Spezial-Angriff."}, "opportunist": {"name": "Profiteur", "description": "Wenn ein Statuswert eines Gegners steigt, profitiert das Pokémon ebenfalls davon und der gleiche Statuswert steigt auch bei ihm."}, "cudChew": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>n ein <PERSON> eine Beere isst, stößt es diese am Ende der nächsten Runde wieder aus seinem Magen auf und verspeist diese erneut."}, "sharpness": {"name": "Scharfkantig", "description": "Die Stärke von <PERSON>itt-Attacken des Pokémon steigt."}, "supremeOverlord": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Bei Kampfantritt steigen der Angriff und Spezial-Angriff des Pokémon ein bisschen für jedes bis dahin besiegte Team-Mitglied."}, "costar": {"name": "S<PERSON><PERSON><PERSON>uf<PERSON>tt", "description": "Das Pokémon kopiert bei Kampfantritt die Statusveränderungen eines Mitstreiters."}, "toxicDebris": {"name": "Giftbelag", "description": "<PERSON><PERSON><PERSON><PERSON> das Pokémon Schaden durch eine physische Attacke, verstreut es giftige Stacheln auf der gegnerischen Seite."}, "armorTail": {"name": "Schweifrüstung", "description": "Der rätselhafte Schweif, der den Kopf des Pokémon umhüllt, hind<PERSON>, Erstschlag-Attacken gegen die Mitstreiterseite einzusetzen."}, "earthEater": {"name": "<PERSON><PERSON>sch<PERSON><PERSON>", "description": "Wird das <PERSON> von einer Boden-Attacke getroffen, erleidet es keinen Schaden, sondern regeneriert stattdessen KP."}, "myceliumMight": {"name": "Myzelienkraft", "description": "<PERSON><PERSON> von Status-Attacken handelt das Pokémon stets langsamer, aber dafür kann es sie ungeachtet der Fähigkeit des Zieles einsetzen."}, "mindsEye": {"name": "Geistiges Auge", "description": "Die Genauigkeit des Pokémon kann nicht gesenkt werden. Es ignoriert Änderungen am Ausweichwert des Zieles und trifft mit Normal- und Kampf-Attacken Geister-Pokémon."}, "supersweetSyrup": {"name": "Süßer Nektar", "description": "<PERSON><PERSON>fantritt verbreitet das Pokémon den Duft süßen Nektars und senkt so den Ausweichwert seiner Gegner."}, "hospitality": {"name": "Gastlichkeit", "description": "<PERSON><PERSON> Kampfantritt zeigt das Pokémon seine Gastlichkeit, indem es die KP seines Mitstreiters ein wenig auffüllt."}, "toxicChain": {"name": "Giftkette", "description": "Durch die toxischen Stoffe in seiner Kette werden Ziele, die das Pokémon mit einer Attacke trifft, gelegentlich schwer vergiftet."}, "embodyAspectTeal": {"name": "Erinnerungskraft", "description": "Die Erinnerungen, die das Pokémon in sich trägt, lassen die Türkisgrüne Maske aufleuchten und erhöhen seine Initiative."}, "embodyAspectWellspring": {"name": "Erinnerungskraft", "description": "Die Erinnerungen, die das Pokémon in sich trägt, lassen die Brunnenmaske aufleuchten und erhöhen seine Spezial-Verteidigung."}, "embodyAspectHearthflame": {"name": "Erinnerungskraft", "description": "Die Erinnerungen, die das Pokémon in sich trägt, lassen die Ofenmaske aufleuchten und erhöhen seinen Angriff."}, "embodyAspectCornerstone": {"name": "Erinnerungskraft", "description": "Die Erinnerungen, die das Pokémon in sich trägt, lassen die Fundamentmaske aufleuchten und erhöhen seine Verteidigung."}, "teraShift": {"name": "Tera-Wandel", "description": "Bei Kampfantritt absorbiert das Pokémon Energie in seiner Umgebung und nimmt die Terakristall-Form an."}, "teraShell": {"name": "Tera-Panzer", "description": "Der Panzer des Pokémon birgt die Kraft aller Typen in sich. Alle Schaden verursachenden Attacken, die es bei vollen KP treffen, sind nicht sehr effektiv."}, "teraformZero": {"name": "Teraforming Null", "description": "<PERSON>n Terapago<PERSON> die Stellarform annimmt, eliminiert es dank seiner verborgenen Kräfte sämtliche Wettereffekte und Felder."}, "poisonPuppeteer": {"name": "Giftpuppenspiel", "description": "<PERSON><PERSON> das Pokémon ein Ziel mit einer Attacke vergiftet, so wird dieses auch verwirrt."}}