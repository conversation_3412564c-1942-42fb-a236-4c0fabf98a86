name: CI

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  # Checks if any files end with `messages.json` and fails if any are found
  validate-filenames:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Filename Inspector
        uses: scheduleonce/lint-filenames@v2.0.0
        with:
          pattern: ^(?!.*messages\.json$).* # Match files that do NOT end with 'messages.json'
          path: "**/*.json"
