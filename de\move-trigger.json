{"hitWithRecoil": "{{pokemon<PERSON>ame}} erleidet Sc<PERSON>en durch <PERSON>ück<PERSON>!", "cutHpPowerUpMove": "{{pokemon<PERSON>ame}} nutzt seine KP um seine Attacke zu verstärken!", "absorbedElectricity": "{{pokemon<PERSON>ame}} absorbiert elektrische Energie!", "switchedStatChanges": "{{pokemon<PERSON>ame}} tauschte die Statuswerteveränderungen mit dem Ziel!", "switchedTwoStatChanges": "{{pokemonName}} tauscht Veränderungen an {{firstStat}} und {{secondStat}} mit dem Ziel!", "switchedStat": "{{pokemon<PERSON>ame}} tauscht seinen {{stat}}-Wert mit dem des Zieles!", "sharedGuard": "{{pokemon<PERSON>ame}} addiert seine <PERSON>hutzkräfte mit jenen des Zieles und teilt sie gerecht auf!", "sharedPower": "{{pokemon<PERSON>ame}} addiert seine Kräfte mit jenen des Zieles und teilt sie gerecht auf!", "shiftedStats": "{{pokemonName}} tauscht den Wert seines {{statToSwitch}}-Werts mit seinem {{statToSwitchWith}}-Werts!", "goingAllOutForAttack": "{{pokemon<PERSON>ame}} legt sich ins Zeug!", "regainedHealth": "{{pokemon<PERSON>ame}} erholt sich!", "keptGoingAndCrashed": "{{pokemon<PERSON>ame}} springt daneben und verletzt sich!", "fled": "{{pokemon<PERSON>ame}} ist geflüchtet!", "cannotBeSwitchedOut": "{{pokemon<PERSON>ame}} kann nicht ausgewechselt werden!", "swappedAbilitiesWithTarget": "{{pokemon<PERSON>ame}} tauscht Fähigkeiten mit dem Ziel!", "coinsScatteredEverywhere": "Es sind überall Münzen verstreut!", "attackedByItem": "{{pokemonName}} wird von seinem Item {{itemName}} angegriffen!", "whippedUpAWhirlwind": "{{pokemon<PERSON>ame}} erzeugt eine Windböe!", "flewUpHigh": "{{poke<PERSON><PERSON><PERSON>}} fliegt hoch empor!", "tookInSunlight": "{{pokemon<PERSON>ame}} absorbiert Sonnen<PERSON>t!", "dugAHole": "{{poke<PERSON><PERSON><PERSON>}} vergräbt sich in der Erde!", "loweredItsHead": "{{pokemon<PERSON>ame}} zieht seinen <PERSON> ein!", "isGlowing": "{{pokemon<PERSON>ame}} leuchtet grell!", "bellChimed": "Eine Glocke läutet!", "foresawAnAttack": "{{pokemon<PERSON>ame}} sieht einen Angriff voraus!", "isTighteningFocus": "{{pokemon<PERSON>ame}} konzen<PERSON>ert sich!", "lostFocus": "{{pokemon<PERSON>ame}} kann sich nicht mehr konzentrieren. Es kann nicht angreifen!", "hidUnderwater": "{{pokemon<PERSON>ame}} taucht unter!", "soothingAromaWaftedThroughArea": "Ein wohltuendes Aroma breitet sich aus!", "sprangUp": "{{pokemon<PERSON>ame}} springt hoch in die Luft!", "choseDoomDesireAsDestiny": "{{pokemon<PERSON>ame}} äußert einen Kismetwunsch für die Zukunft!", "vanishedInstantly": "{{pokemon<PERSON>ame}} verschwindet augenblicklich!", "tookTargetIntoSky": "{{pokemonName}} entführt {{targetName}} in luftige Höhen!", "becameCloakedInFreezingLight": "{{pokemon<PERSON>ame}} wird von einem kühlen Licht umhüllt!", "becameCloakedInFreezingAir": "{{pokemon<PERSON>ame}} wird in klirrend kalte Luft gehüllt!", "isChargingPower": "{{pokemon<PERSON>ame}} saugt <PERSON> in sich auf!", "burnedItselfOut": "{{pokemon<PERSON>ame}} braucht sein <PERSON>uer komplett auf!", "startedHeatingUpBeak": "{{pokemon<PERSON>ame}} erhitzt seinen Schnabel!", "setUpShellTrap": "{{poke<PERSON><PERSON><PERSON>}} hat eine Panzerfalle gelegt!", "isOverflowingWithSpacePower": "Kosmische Kräfte strömen aus {{pokemonName}}!", "usedUpAllElectricity": "{{pokemon<PERSON>ame}} braucht seinen Strom komplett auf!", "stoleItem": "{{pokemonName}} hat {{targetName}} das Item {{itemName}} geklaut!", "incineratedItem": "{{pokemonName}} hat {{itemName}} von {{targetName}} verbrannt. Es ist somit nutzlos geworden!", "knockedOffItem": "{{pokemonName}} schl<PERSON>gt das Item {{itemName}} von {{targetName}} weg!", "tookMoveAttack": "{{pokemonName}} wurde von {{moveName}} getroffen!", "cutOwnHpAndMaximizedStat": "{{pokemonName}} nutzt seine KP und maximiert dadurch seinen {{statName}}-Wert!", "copiedStatChanges": "{{pokemonName}} kopiert die Statusveränderungen von {{targetName}}!", "magnitudeMessage": "Intensität {{magnitude}}!", "tookAimAtTarget": "{{pokemonName}} zielt auf {{targetName}}!", "transformedIntoType": "{{pokemonName}} nimmt den Typ {{typeName}} an!", "copiedMove": "{{pokemonName}} kopiert {{moveName}}!", "sketchedMove": "{{pokemonName}} ahmt die Attacke {{moveName}} nach!", "acquiredAbility": "The {{pokemonName}} nimmt die Fähigkeit {{abilityName}} an!", "copiedTargetAbility": "{{pokemonName}} kopiert {{abilityName}} von {{targetName}}!", "transformedIntoTarget": "{{pokemonName}} verwandelt sich in {{targetName}}!", "tryingToTakeFoeDown": "{{pokemon<PERSON>ame}} vers<PERSON><PERSON>, den Angreifer mit sich zu nehmen!", "addType": "{{pokemonName}} nimmt zus<PERSON>z<PERSON> den Typ {{typeName}} an!", "cannotUseMove": "{{pokemonName}} kann {{moveName}} nicht einsetzen!", "healHp": "<PERSON><PERSON> von {{pokemon<PERSON>ame}} wurden aufgefrischt!", "sacrificialFullRestore": "<PERSON> Heilopfer von {{pokemonName}} erreicht sein <PERSON>!", "invertStats": "Alle Statusveränderungen von {{pokemonName}} wurden invertiert!", "resetStats": "Die Statusveränderungen von {{pokemonName}} wurden aufgehoben!", "statEliminated": "Alle Statusveränderungen wurden aufgehoben!", "faintCountdown": "{{pokemon<PERSON>ame}} geht nach {{turnCount}} Runden K.O.!", "copyType": "{{pokemonName}} hat den Typ von {{targetPokemonName}} angenommen!", "suppressAbilities": "Die Fähigkeit von {{pokemonName}} wirkt nicht mehr!", "revivalBlessing": "{{pokemon<PERSON>ame}} ist wieder fit und kampfbereit!", "swapArenaTags": "{{pokemon<PERSON>ame}} hat die Effekte, die auf den beiden Seiten des Kampffeldes wirken, mitein<PERSON> get<PERSON>cht!", "chillyReception": "{{pokemon<PERSON>ame}} erzählt einen schlechten Witz, der nicht besonders gut ankommt…", "exposedMove": "{{pokemonName}} erkennt {{targetPokemonName}}!", "safeguard": "{{targetName}} wird durch Bodyguard geschützt!", "restBecameHealthy": "{{poke<PERSON><PERSON><PERSON>}} hat im Schlaf Energie getankt!", "substituteOnOverlap": "{{poke<PERSON><PERSON><PERSON>}} hat bereits einen Delegator!", "substituteNotEnoughHp": "Es ist zu schwach, um einen Delegator einzusetzen!", "afterYou": "{{targetName}} lässt sich auf Galanterie ein!", "combiningPledge": "Zwei Attacken bilden zusammen eine Kombi-Attacke!", "awaitingPledge": "{{userPokemonName}} wartet auf {{allyPokemonName}}…", "corrosiveGasItem": "{{pokemonName}} hat das Item {{itemName}} von {{targetName}} zersetzt!", "instructingMove": "{{targetPokemonName}} führt seine zuletzt eingesetzte Attacke auf Befehl von {{userPokemonName}} erneut aus!", "lunarDanceRestore": "{{pokemon<PERSON>ame}} wird in mysteriöses Mondlicht getaucht!", "stealPositiveStats": "{{pokemon<PERSON>ame}} hat erhöhte Statuswerte gestohlen!", "naturePowerUse": "<PERSON><PERSON><PERSON><PERSON><PERSON> von {{pokemonName}} löst {{moveName}} aus!", "forceLast": "{{target<PERSON>okemonName}} muss sich hinten anstellen!", "splash": "Nichts geschieht!", "fallDown": "{{targetPokemonName}} ist herabgestürzt!", "celebrate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, {{playerName}}!"}