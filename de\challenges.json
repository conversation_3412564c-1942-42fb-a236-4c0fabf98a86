{"title": "Herausforderungsmodifikatoren", "illegalEvolution": "{{pokemon}} hat sich in ein <PERSON> ve<PERSON>wan<PERSON>, dass für diese Herausforderung nicht zulässig ist!", "noneSelected": "<PERSON><PERSON> ausgewähl<PERSON>", "freshStart": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value.0": "Aus"}, "limitedCatch": {"name": "Fangbegrenzung", "desc": "Du kannst nur das erste Pokémon eines Bioms zu deinem Team hinzufügen.", "value.0": "Aus", "value.1": "An"}, "singleGeneration": {"name": "Mono-Generation", "desc": "Du kannst nur Pokémon aus der {{gen}} Generation verwenden.", "desc_default": "Du kannst nur Pokémon aus der gewählten Generation verwenden.", "gen_1": "ersten", "gen_2": "zweiten", "gen_3": "dritten", "gen_4": "vierten", "gen_5": "fünften", "gen_6": "<PERSON><PERSON><PERSON>", "gen_7": "si<PERSON><PERSON>", "gen_8": "achten", "gen_9": "neunten"}, "singleType": {"name": "Mono-Typ", "desc": "Du kannst nur Pokémon des Typs {{type}} verwenden.", "desc_default": "Du kannst nur Pokémon des gewählten Typs verwenden."}, "inverseBattle": {"name": "Umkehrkampf", "shortName": "Umkehrkampf", "desc": "Die Typen-Effektivität wird umgekehrt, und kein Typ ist gegen einen anderen Typ immun.\nDeaktiviert die Erfolge anderer Herausforderungen.", "value.0": "Aus", "value.1": "An"}, "flipStat": {"name": "Statuswert-Flip", "shortName": "Umkehren", "desc": "Die Werte werden neu gemischt. KP und Init tauschen.\nAngriff wird zu Sp.-Vert.\nVert. wird zu Sp.-Ang.\nSp.-Vert. wird zu Angriff.\nSp.-Ang. wird zu Vert.\nDeaktiviert die Erfolge anderer Herausforderungen.", "value.0": "Aus", "value.1": "An"}, "metronome": {"name": "Metronom-Modus", "desc": "Du darfst nur das Metronom verwenden! <PERSON><PERSON><PERSON>, wie weit du mit purem Zufall kommst.\nDieser Modus ist AUSSCHLIESSLICH zum Spaß und schaltet keine Erfolge frei. Deaktiviert alle anderen Erfolge.", "value.0": "Aus", "value.1": "An"}}