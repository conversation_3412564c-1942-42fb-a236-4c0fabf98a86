{"intro": "It’s a teacher and some school children!", "speaker": "Teacher", "intro_dialogue": "Hello, there! Would you be able to\nspare a minute for my students?$I’m teaching them about Pokémon moves\nand would love to show them a demonstration.$Would you mind showing us one of\nthe moves your Pokémon can use?", "title": "Field Trip", "description": "A teacher is requesting a move demonstration from a Pokémon. Depending on the move you choose, she might have something useful for you in exchange.", "query": "Which move category will you show off?", "option": {"1": {"label": "A Physical Move", "tooltip": "(+) <PERSON> <PERSON><PERSON>"}, "2": {"label": "A Special Move", "tooltip": "(+) <PERSON> <PERSON><PERSON>"}, "3": {"label": "A Status Move", "tooltip": "(+) Status Item <PERSON>s"}, "selected": "{{poke<PERSON>ame}} shows off an awesome display of {{move}}!"}, "second_option_prompt": "Choose a move for your Pokémon to use.", "incorrect": "…$That isn’t a {{moveCategory}} move!\nI’m sorry, but I can’t give you anything.$Come along children, we’ll\nfind a better demonstration elsewhere.", "incorrect_exp": "Looks like you learned a valuable lesson?$Your Pokémon also gained some experience.", "correct": "Thank you so much for your kindness!\nI hope these items might be of use to you!", "correct_exp": "{{poke<PERSON><PERSON>}} also gained some valuable experience!", "status": "Status", "physical": "Physical", "special": "Special"}