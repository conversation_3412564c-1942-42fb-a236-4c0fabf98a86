{"yourTeam": "Pokémon auf deiner Seite", "opposingTeam": "Pokémon auf gegnerischer Seite", "arenaOnRemove": "Der Effekt von {{moveName}} lässt nach!", "arenaOnRemovePlayer": "Der Effekt von {{moveName}} lässt auf deiner Seite nach!", "arenaOnRemoveEnemy": "Der Effekt von {{moveName}} lässt auf der Seite des Gegners nach!", "mistOnAdd": "Pokémon, die auf der Seite von {{pokemonNameWithAffix}} kämp<PERSON>, werden in Weißnebel gehüllt!", "mistApply": "Der Weißnebel verhindert die Senkung von Statuswerten!", "reflectOnAdd": "Reflektor stärkt Pokémon gegen physische Attacken!", "reflectOnAddPlayer": "Reflektor stärkt Pokémon auf deiner Seite gegen physische Attacken!", "reflectOnAddEnemy": "Reflektor stärkt gegnerische Pokémon gegen physische Attacken!", "lightScreenOnAdd": "Lichtschild stärkt Pokémon gegen Spezial-Attacken!", "lightScreenOnAddPlayer": "Lichtschild stärkt Pokémon, die auf deiner Seite kämpfen, gegen Spezial-Attacken!", "lightScreenOnAddEnemy": "Lichtschild stärkt gegnerische Pokémon gegen Spezial-Attacken!", "auroraVeilOnAdd": "Auroraschleier stärkt Pokémon gegen physische und Spezial-Attacken!", "auroraVeilOnAddPlayer": "Auroraschleier stärkt Pokémon auf deiner Seite gegen physische und Spezial-Attacken!", "auroraVeilOnAddEnemy": "Auroraschleier stärkt gegnerische Pokémon gegen physische und Spezial-Attacken!", "conditionalProtectOnAdd": "Die Pokémon werden von {{moveName}} behütet!", "conditionalProtectOnAddPlayer": "Die Pokémon auf deiner Seite werden von {{moveName}} behütet!", "conditionalProtectOnAddEnemy": "Die Pokémon auf der gegnerischen Seite werden von {{moveName}} behütet!", "conditionalProtectApply": "{{pokemonNameWithAffix}} wird durch {{moveName}} geschützt!", "matBlockOnAdd": "{{pokemonNameWithAffix}} bringt seinen Tatami-<PERSON><PERSON><PERSON> in Position!", "noCritOnAddPlayer": "{{moveName}} schützt dein Team vor Volltreffern!", "noCritOnAddEnemy": "{{moveName}} schützt das gegnerische Team vor Volltreffern!", "noCritOnRemove": "{{moveName}} von {{pokemonNameWithAffix}} hört auf zu wirken!", "wishTagOnAdd": "Der Wunschtraum von {{pokemonNameWithAffix}} erfüllt sich!", "mudSportOnAdd": "Die Stärke aller Elektro-Attacken wurde reduziert!", "mudSportOnRemove": "<PERSON><PERSON><PERSON><PERSON> hört auf zu wirken!", "waterSportOnAdd": "Die Stärke aller Feuer-Attacken wurde reduziert!", "waterSportOnRemove": "Nassma<PERSON> hört auf zu wirken!", "plasmaFistsOnAdd": "Ein elektrisch geladener Niederschlag regnet auf das Kampffeld herab!", "spikesOnAdd": "Die {{opponentDesc}} sind von Stacheln umgeben!", "spikesActivateTrap": "Die {{pokemonNameWithAffix}} wurde durch Stachler verletzt!", "toxicSpikesOnAdd": "Die {{opponentDesc}} sind überall von giftigen Stacheln umgeben!", "toxicSpikesActivateTrapPoison": "{{pokemonNameWithAffix}} absorbiert die {{moveName}}!", "stealthRockOnAdd": "Um die {{opponentDesc}} schweben spitze Steine!", "stealthRockActivateTrap": "{{pokemonNameWithAffix}} wird von spitzen <PERSON> getroffen!", "stickyWebOnAdd": "Am <PERSON>den um {{opponentDesc}} entspinnt sich ein {{moveName}}!", "stickyWebActivateTrap": "{{pokemon<PERSON>ame}} ist im Klebenetz gefangen!", "trickRoomOnAdd": "{{pokemonNameWithAffix}} hat die Dimensionen verdreht!", "trickRoomOnRemove": "Die verdrehte Dimension ist wieder normal!", "gravityOnAdd": "Die Erdanziehung wurde verstärkt!", "gravityOnRemove": "Die Erdanziehung ist wieder normal!", "tailwindOnAdd": "Die Pokémon erhalten Rückenwind!", "tailwindOnAddPlayer": "Die Pokémon, die auf deiner Seite kämpfen, erhalten Rückenwind!", "tailwindOnAddEnemy": "Die gegnerischen Pokémon erhalten Rückenwind!", "tailwindOnRemove": "Der Rückenwind hat sich gelegt!", "tailwindOnRemovePlayer": "Der Rückenwind auf deiner Seite hat sich gelegt!", "tailwindOnRemoveEnemy": "Der Rückenwind auf gegnerischer Seite hat sich gelegt!", "happyHourOnAdd": "Goldene Zeiten sind angebrochen!", "happyHourOnRemove": "Die goldenen Zeiten sind vorbei!", "safeguardOnAdd": "Das ganze Feld wird von einem Schleier umhüllt!", "safeguardOnAddPlayer": "Das Team des Anwenders wird von einem Schleier umhüllt!", "safeguardOnAddEnemy": "Das gegnerische Team wird von einem Schleier umhüllt!", "safeguardOnRemove": "Der mystische Schleier, der das ganze Feld umgab, hat sich gelüftet!", "safeguardOnRemovePlayer": "Der mystische Schleier, der dein Team umgab, hat sich gelüftet!", "safeguardOnRemoveEnemy": "Der mystische Schleier, der das gegnerische Team umgab, hat sich gelüftet!", "fireGrassPledgeOnAdd": "Es erstreckt sich ein Me<PERSON> aus Feuer!", "fireGrassPledgeOnAddPlayer": "Um die Pokémon auf deiner Se<PERSON> erstreckt sich ein Meer aus Feuer!", "fireGrassPledgeOnAddEnemy": "Um die Pokémon auf der gegnerischen Seite erstreckt sich ein Me<PERSON> aus Feuer!", "fireGrassPledgeLapse": "{{pokemonNameWithAffix}} nimmt Schaden durch das Meer aus Feuer!", "waterFirePledgeOnAdd": "Ein Regenbogen erscheint am Himmel!", "waterFirePledgeOnAddPlayer": "Ein Regenbogen erscheint am Himmel über den Pokémon auf deiner Seite!", "waterFirePledgeOnAddEnemy": "Ein Regenbogen erscheint am Himmel über den Pokémon auf der gegnerischen Seite!", "grassWaterPledgeOnAdd": "Ein Sumpf tut sich auf!", "grassWaterPledgeOnAddPlayer": "Ein Sumpf tut sich um die Pokémon auf deiner Seite auf!", "grassWaterPledgeOnAddEnemy": "Ein Sumpf tut sich um die Pokémon auf der gegnerischen Seite auf!", "fairyLockOnAdd": "Während der nächsten Runde ist keine Flucht möglich!", "neutralizingGasOnAdd": "Reaktionsgas von {{pokemonNameWithAffix}} hat sich in der Umgebung ausgebreitet!", "neutralizingGasOnRemove": "Das Reaktionsgas hört auf zu wirken!"}