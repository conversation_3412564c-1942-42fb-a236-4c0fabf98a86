{"pound": {"name": "<PERSON>lap<PERSON>", "effect": "Ein Hieb mit den Vorderbeinen oder dem Schweif."}, "karateChop": {"name": "Karateschlag", "effect": "<PERSON><PERSON> Möglichkeit, einen Volltreffer zu landen."}, "doubleSlap": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Trifft das Ziel zwei- bis fünfmal hintereinander mit einem Duplexhieb."}, "cometPunch": {"name": "Kometenhieb", "effect": "Trifft das Ziel zwei- bis fünfmal hintereinander mit kräftigen Hieben."}, "megaPunch": {"name": "<PERSON><PERSON><PERSON>", "effect": "Ein unglaublich kräftiger Hieb."}, "payDay": {"name": "Zahltag", "effect": "Das Ziel wird mit Münzen beworfen. Das Geld wird nach dem Kampf aufgesammelt."}, "firePunch": {"name": "Feuerschlag", "effect": "<PERSON><PERSON><PERSON>, der dem Ziel eventuell Verbrennungen zufügt."}, "icePunch": {"name": "<PERSON><PERSON><PERSON>", "effect": "Ein eisiger Schlag, der das Ziel eventuell einfriert."}, "thunderPunch": {"name": "Donnerschlag", "effect": "Ein elektrischer Schlag, der das Ziel eventuell paralysiert."}, "scratch": {"name": "<PERSON><PERSON><PERSON>", "effect": "Das Ziel wird mit scharfen Klauen zerkratzt."}, "viseGrip": {"name": "<PERSON><PERSON><PERSON>", "effect": "Das Ziel wird umklammert und zusammengequetscht."}, "guillotine": {"name": "Guillotine", "effect": "Kräftige Scheren-Attacke. Führt beim Ziel sofort zum K.O."}, "razorWind": {"name": "Klingensturm", "effect": "<PERSON><PERSON> Attacke, die über zwei Runden geht. Hohe Volltrefferquote."}, "swordsDance": {"name": "Schwerttanz", "effect": "Ein wilder Kampftanz, der den eigenen Angriffs-Wert stark erhöht."}, "cut": {"name": "Zerschneider", "effect": "Ein Basisangriff mit Schere oder Klaue. Damit können kleine Bäume gefällt werden."}, "gust": {"name": "<PERSON><PERSON><PERSON>", "effect": "Trifft das Ziel mit einem Windstoß durch einen Flügelschlag."}, "wingAttack": {"name": "Flügelschlag", "effect": "Trifft das Ziel mit ausgebreiteten Flügeln."}, "whirlwind": {"name": "<PERSON><PERSON>bel<PERSON>", "effect": "<PERSON>ht das Ziel weg und ersetzt es durch ein anderes Pokémon. In der Wildnis endet der Kampf."}, "fly": {"name": "Fliegen", "effect": "Steigt in Runde 1 empor und trifft das Ziel in Runde 2."}, "bind": {"name": "Klammergriff", "effect": "Umklammert und quetscht das Ziel über vier bis fünf Runden."}, "slam": {"name": "Slam", "effect": "<PERSON><PERSON>ag mit einem langen Schweif, einer Ranke oder Ähnlichem."}, "vineWhip": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Peitschenähnlicher Schlag mit Ranken."}, "stomp": {"name": "Stampfer", "effect": "Stampfen mit dem Fuß. Das Ziel schreckt eventuell zurück."}, "doubleKick": {"name": "<PERSON><PERSON><PERSON>", "effect": "Der Anwender tritt in einer Runde zweimal schnell zu."}, "megaKick": {"name": "Megakick", "effect": "Das Ziel wird mit einem extrem heftigen Tritt angegriffen."}, "jumpKick": {"name": "Sprungkick", "effect": "Der Angreifer hüpft hoch und tritt zu. Bei Misserfolg schadet er sich selbst."}, "rollingKick": {"name": "Fegekick", "effect": "Heftiger Tritt aus einer schnellen Drehbewegung. Lässt das Ziel eventuell zurückschrecken."}, "sandAttack": {"name": "Sand<PERSON><PERSON><PERSON>", "effect": "Senkt Genauigkeit des Zieles, indem ihm Sand ins Gesicht geworfen wird."}, "headbutt": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Rammt das Ziel mit einer Kopfnuss. Z<PERSON> schreckt eventuell zurück."}, "hornAttack": {"name": "Hornattacke", "effect": "Spießt das Ziel mit einem spitzen Horn auf."}, "furyAttack": {"name": "Furienschlag", "effect": "Spießt das Ziel zwei- bis fünfmal mit spitzem Horn oder Schnabel auf."}, "hornDrill": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "K.O.<PERSON><PERSON><PERSON>, bei der ein Horn als Bohrer eingesetzt wird."}, "tackle": {"name": "Tackle", "effect": "Trifft das Ziel mit vollem Körpereinsatz."}, "bodySlam": {"name": "Bodyslam", "effect": "Trifft das Ziel mit vollem Körpereinsatz. Bewirkt eventuell Paralyse."}, "wrap": {"name": "<PERSON><PERSON><PERSON>", "effect": "Umwickelt das Ziel über vier bis fünf Runden mit Ranken oder Ähnlichem und fügt ihm Schaden zu."}, "takeDown": {"name": "Bodycheck", "effect": "Rücksichtslose Attacke, bei der sich der Angreifer selbst leicht verletzt."}, "thrash": {"name": "<PERSON><PERSON><PERSON>", "effect": "Attacke über zwei bis drei Runden, die den Angreifer verwirrt."}, "doubleEdge": {"name": "Risikotackle", "effect": "Lebensgefährlicher Angriff, bei dem sich der Angreifer selbst verletzt."}, "tailWhip": {"name": "Rutenschlag", "effect": "Hieb mit dem Schweif. Senkt die Verteidigung des Zieles."}, "poisonSting": {"name": "Giftstachel", "effect": "Angriff mit Giftstachel. Das Ziel wird eventuell vergiftet."}, "twineedle": {"name": "Duonadel", "effect": "Stacheln treffen das Ziel zweimal. Das Ziel wird eventuell vergiftet."}, "pinMissile": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Spitze Nadeln treffen das Ziel zwei- bis fünfmal hintereinander."}, "leer": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Gegnerischer Verteidigungs-Wert wird durch angsteinflößenden Blick gesenkt."}, "bite": {"name": "<PERSON><PERSON>", "effect": "Be<PERSON>ßt zu und lässt das Ziel eventuell zurückschrecken."}, "growl": {"name": "<PERSON>uler", "effect": "Der Anwender nimmt das Ziel für sich ein und senkt dessen Angriffs-Wert."}, "roar": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Ver<PERSON><PERSON> das Ziel und ersetzt es durch ein anderes Pokémon. Beendet den Kampf in der Wildnis."}, "sing": {"name": "G<PERSON><PERSON>", "effect": "Ein Lied, das das Ziel in tiefen Schlaf versetzt."}, "supersonic": {"name": "Superschall", "effect": "Auss<PERSON><PERSON> bizarrer <PERSON>wellen. Das Ziel wird verwirrt."}, "sonicBoom": {"name": "Ultraschall", "effect": "Das Ziel wird von einer Schockwelle getroffen, die stets 20 KP Schaden anrichtet."}, "disable": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Die zuletzt eingesetzte Attacke des Zieles wird für mehrere Runden blockiert."}, "acid": {"name": "<PERSON>ä<PERSON>", "effect": "Versprüht ätzende Flüssigkeit, die eventuell die Spezial-Verteidigung der Gegner in der Nähe des Anwenders senkt."}, "ember": {"name": "Glut", "effect": "<PERSON><PERSON><PERSON> Feuer-Attacke, durch die das Ziel eventuell Verbrennungen erleidet."}, "flamethrower": {"name": "Flammenwurf", "effect": "<PERSON><PERSON><PERSON><PERSON>, durch die das Ziel eventuell Verbrennungen erleidet."}, "mist": {"name": "<PERSON><PERSON><PERSON>bel", "effect": "<PERSON><PERSON><PERSON> schützt das Team mit einem Nebel. Verhindert Statussenkungen für fünf Runden."}, "waterGun": {"name": "Aquaknarre", "effect": "Das Ziel wird mit Was<PERSON> bespritzt."}, "hydroPump": {"name": "Hydropumpe", "effect": "<PERSON><PERSON><PERSON><PERSON> eine Menge Wasser mit Hochdruck auf das Ziel."}, "surf": {"name": "Surfer", "effect": "Eine Welle bricht über alle Pokémon in der Nähe des Anwenders herein."}, "iceBeam": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Das Ziel wird von einem Eisstrahl getroffen und friert eventuell ein."}, "blizzard": {"name": "Blizzard", "effect": "Ein Schneesturm wütet, der das Ziel einfrieren kann."}, "psybeam": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON>t einen Strahl ab, der das Ziel verwirren kann."}, "bubbleBeam": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, die eventuell den Initiative-Wert des Zieles senken."}, "auroraBeam": {"name": "Aurorastrahl", "effect": "Regenbogenfarbener Strahl, der eventuell den Angriffs-Wert des Zieles senkt."}, "hyperBeam": {"name": "Hyperstrahl", "effect": "<PERSON><PERSON>, die den Anwender zwingt, eine Runde auszusetzen."}, "peck": {"name": "<PERSON><PERSON><PERSON>", "effect": "<PERSON>reift das Ziel mit dem Schnabel oder Horn an."}, "drillPeck": {"name": "Bohrschnabel", "effect": "<PERSON><PERSON><PERSON><PERSON>her<PERSON><PERSON>, bei dem der Schnabel als Bohrer dient."}, "submission": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON> Körpera<PERSON>cke, bei der sich der Angreifer selbst leicht verletzt."}, "lowKick": {"name": "<PERSON><PERSON><PERSON>", "effect": "<PERSON>, der das Ziel umwirft. Je schwerer das Ziel ist, desto mehr Schaden fügt ihm die Attacke zu."}, "counter": {"name": "<PERSON><PERSON>", "effect": "Kontert physische Treffer und fügt dem Ziel das Doppelte des Schadens zu, den der Anwender erlitten hat."}, "seismicToss": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Ziel wird mit der Kraft der Gravitation umgeworfen. Richtet Schaden gemäß Level des Angreifers an."}, "strength": {"name": "Stärke", "effect": "Das Ziel wird extrem stark getroffen. <PERSON>ht Verschieben von <PERSON>en möglich."}, "absorb": {"name": "Absorber", "effect": "<PERSON><PERSON>, die die Hälfte des Schadens absorbiert."}, "megaDrain": {"name": "Megasauger", "effect": "<PERSON><PERSON>, die die Hälfte des Schadens absorbiert."}, "leechSeed": {"name": "Egelsamen", "effect": "<PERSON><PERSON> wird bepflanzt und verliert jede Runde KP, die ein Pokémon aus dem Team des Anwenders heilen."}, "growth": {"name": "Wach<PERSON><PERSON>", "effect": "Der Körper wächst. Dadurch steigen Angriff und Spezial-Angriff."}, "razorLeaf": {"name": "Rasierblatt", "effect": "Trifft das Ziel mit Blättern. Hohe Volltrefferquote."}, "solarBeam": {"name": "Solarstrahl", "effect": "Absorbiert Licht in Runde 1. In Runde 2 erfolgt der Angriff."}, "poisonPowder": {"name": "Giftpuder", "effect": "Verstreut giftigen Puder auf das Ziel."}, "stunSpore": {"name": "Stachelspore", "effect": "Verstreut lähmenden Puder."}, "sleepPowder": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON>ers<PERSON>ut Schlafpuder, der das Ziel eventuell in Schlaf versetzt."}, "petalDance": {"name": "Blättertanz", "effect": "Angriff mit Blütenblättern für zwei bis drei Runden. Angreifer wird verwirrt."}, "stringShot": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Umwickelt Ziele in der Nähe mit Fäden aus dem Mund und senkt den Initiative-Wert."}, "dragonRage": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON>t eine wutgeladene Schockwelle aus, die stets 40 KP Schaden anrichtet."}, "fireSpin": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Das Ziel wird für vier bis fünf Runden in einem Feuerkreis gefangen."}, "thunderShock": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON>e Elektro-Attacke, die das Ziel eventuell paralysiert."}, "thunderbolt": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON>e starke Elektro-Attacke, die das Ziel eventuell paralysiert."}, "thunderWave": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Ein schwacher Stromstoß, der das Ziel paralysiert."}, "thunder": {"name": "<PERSON><PERSON>", "effect": "Eine verheerende Elektro-Attacke, die das Ziel eventuell paralysiert."}, "rockThrow": {"name": "<PERSON><PERSON><PERSON>", "effect": "Das Ziel wird mit einem kleinen Stein beworfen."}, "earthquake": {"name": "<PERSON>rdbeben", "effect": "Ein mächtiges Beben, das die anderen Pokémon in der Nähe des Anwenders trifft."}, "fissure": {"name": "Geofissur", "effect": "Das Ziel wird in eine Erdspalte geworfen. Ist die Attacke erfolgreich, führt sie zu einem K.O."}, "dig": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "In Runde 1 gräbt sich der Anwender ein und in Runde 2 greift er an. Macht Flucht aus Höhlen möglich."}, "toxic": {"name": "Toxin", "effect": "Vergiftet das Ziel mit einem potenten Toxin schwer. Vergiftung wird von Runde zu Runde stärker."}, "confusion": {"name": "Konfusion", "effect": "Das Ziel wird von schwacher telekinetischer Energie getroffen und eventuell verwirrt."}, "psychic": {"name": "Psychokinese", "effect": "<PERSON><PERSON>e, die eventuell die Spezial-Verteidigung des Zieles senkt."}, "hypnosis": {"name": "Hypnose", "effect": "<PERSON>ypnose-<PERSON><PERSON>, die das Ziel in Schlaf versetzt."}, "meditate": {"name": "Meditation", "effect": "Anwender aktiviert Kräfte, die tief in seinem Inneren schlummern, und steigert so seinen Angriffs-Wert."}, "agility": {"name": "Agilität", "effect": "Entspannt den Körper, um den Initiative-Wert stark zu steigern."}, "quickAttack": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON> schneller Angriff mit Erstschlaggarantie."}, "rage": {"name": "Raserei", "effect": "<PERSON><PERSON><PERSON><PERSON>ht Angriff des Anwenders, wenn dieser getroffen wird, solange die Attacke aktiviert ist."}, "teleport": {"name": "Teleport", "effect": "Der Anwender tauscht den Platz mit einem anderen Team-Mitglied, sofern vorhanden. <PERSON>zen wilde Pokémon die Attacke ein, ergreifen diese die Flucht."}, "nightShade": {"name": "Nachtnebel", "effect": "Das Ziel sieht eine Illusion. Richtet Schaden gemäß dem Level des Anwenders an."}, "mimic": {"name": "Mimikry", "effect": "Kopiert die zuvor ausgeführte Attacke des Zieles. Kann im Kampf bis zur Auswechslung verwendet werden."}, "screech": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON>t einen Schrei aus, um die Verteidigung des Zieles stark zu senken."}, "doubleTeam": {"name": "Doppelteam", "effect": "<PERSON><PERSON><PERSON><PERSON> durch schnelle Bewegungen Ebenbilder, um den Fluchtwert zu erhöhen."}, "recover": {"name": "Genesung", "effect": "Eine Selbstheilung. KP des Anwenders werden um 50 % des maximalen Wertes aufgefüllt."}, "harden": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Stärkt die Muskulatur und erhöht den Verteidigungs-Wert."}, "minimize": {"name": "Komprimator", "effect": "<PERSON><PERSON><PERSON> schrumpft, um seinen Fluchtwert stark zu erhöhen."}, "smokescreen": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Senkt Genauigkeit des Zieles mit Rauch, Tinte oder Ähnlichem."}, "confuseRay": {"name": "Konfusstrahl", "effect": "<PERSON> fieser <PERSON>, der das Ziel verwirrt."}, "withdraw": {"name": "Panzerschutz", "effect": "Rückzug in den harten Panzer. Erhöht den Verteidigungs-Wert."}, "defenseCurl": {"name": "<PERSON><PERSON><PERSON>", "effect": "Verbirgt Schwächen durch Einrollen und hebt gleichzeitig den Verteidigungs-Wert."}, "barrier": {"name": "Barriere", "effect": "<PERSON><PERSON><PERSON><PERSON> eine Barriere, die den Verteidigungs-Wert stark erhöht."}, "lightScreen": {"name": "Lichtschild", "effect": "<PERSON><PERSON><PERSON><PERSON> eine Lichtwand und senkt den Schaden durch Spezial-Angriffe für fünf Runden."}, "haze": {"name": "<PERSON><PERSON><PERSON><PERSON>bel", "effect": "<PERSON><PERSON><PERSON><PERSON> einen dunklen Nebel. Alle Veränderungen der Statuswerte der Kampfteilnehmer werden annulliert."}, "reflect": {"name": "Reflektor", "effect": "<PERSON>e mysteriö<PERSON>, die fünf Runden den Schaden von physischen gegnerischen Treffern reduziert."}, "focusEnergy": {"name": "Energiefokus", "effect": "<PERSON><PERSON><PERSON> atmet ein und bündelt Kraft. Die Volltrefferquote steigt dadurch."}, "bide": {"name": "<PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON><PERSON><PERSON> zwei Runden Angriffe und schlägt dann mit dem doppelten Wert des erlittenen Schadens zurück."}, "metronome": {"name": "Metronom", "effect": "<PERSON><PERSON><PERSON>, um das Gehirn zu stimuli<PERSON>n. Wählt zufällig eine Attacke aus."}, "mirrorMove": {"name": "Spiegeltrick", "effect": "Kopiert die letzte Attacke des Zieles und greift es an."}, "selfDestruct": {"name": "Finale", "effect": "<PERSON><PERSON><PERSON> sprengt sich, richtet rundum Riesenschaden an und wird dabei besiegt."}, "eggBomb": {"name": "E<PERSON>bombe", "effect": "Ein großes Ei wird auf das Ziel abgefeuert, um ihm zu schaden."}, "lick": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Leck-Attacke mit langer Zunge. Das Ziel wird eventuell paralysiert."}, "smog": {"name": "Smog", "effect": "Angriff mit Gas. Das Ziel kann eventuell vergiftet werden."}, "sludge": {"name": "Schlammbad", "effect": "Wirft Schlamm auf das Ziel. Dieses wird eventuell vergiftet."}, "boneClub": {"name": "Knochenkeule", "effect": "<PERSON><PERSON><PERSON><PERSON> das Ziel mit einer Keule und lässt es eventuell zurückschrecken."}, "fireBlast": {"name": "Feuersturm", "effect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, die das Ziel versengt und ihm eventuell eine Verbrennung zufügt."}, "waterfall": {"name": "Kaskade", "effect": "Eine mächtige Attacke, durch die das Ziel eventuell zurückschreckt."}, "clamp": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Fängt und quetscht das Ziel über vier bis fünf Runden durch die harte Schale des Anwenders."}, "swift": {"name": "<PERSON><PERSON><PERSON>", "effect": "Vers<PERSON><PERSON><PERSON>t sternförmige Strahlen, die stets treffen, auf Ziele in der Umgebung."}, "skullBash": {"name": "<PERSON><PERSON><PERSON><PERSON>wu<PERSON>", "effect": "Der Anwender erhöht in Runde 1 seine Verteidigung und greift in Runde 2 an."}, "spikeCannon": {"name": "Dornkanone", "effect": "Spitze Nadeln treffen das Ziel zwei- bis fünfmal hintereinander."}, "constrict": {"name": "Umklammerung", "effect": "Angriff mit langen Tentakeln oder Ranken. Senkt eventuell den Initiative-Wert."}, "amnesia": {"name": "Amnesie", "effect": "Gedächtnisverlust, der die Spezial-Verteidigung stark erhöht."}, "kinesis": {"name": "Psykraft", "effect": "Lenkt Ziel durch Verbiegen eines Löffels ab. Senkt dessen Genauigkeit."}, "softBoiled": {"name": "<PERSON><PERSON><PERSON>", "effect": "KP des Anwenders werden um 50 % der maximalen KP aufgefüllt."}, "highJumpKick": {"name": "Turm<PERSON>", "effect": "Sprungtritt mit Knie. Bei Misserfolg verletzt sich der Anwender selbst."}, "glare": {"name": "Schlangenblick", "effect": "<PERSON><PERSON><PERSON><PERSON><PERSON> Ziel mit dem Muster auf seinem Bauch ein, sodass dieses paralysiert wird."}, "dreamEater": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Attacke gegen schlafendes Ziel. Die Hälfte des zugefügten Schadens wird dem Anwender gutgeschrieben."}, "poisonGas": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Hüllt Ziele in der Umgebung in Gas ein, das sie eventuell vergiftet."}, "barrage": {"name": "Stakkato", "effect": "Wirft zwei- bis fünfmal runde Gegenstände auf das Ziel."}, "leechLife": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Die Hälfte des zugefügten Schadens wird dem Anwender gutgeschrieben."}, "lovelyKiss": {"name": "Todeskuss", "effect": "Anwender zwingt dem Ziel einen Kuss auf, der Schlaf verursacht."}, "skyAttack": {"name": "Himmelsfeger", "effect": "<PERSON><PERSON><PERSON> greift in der zweiten Runde mit hoher Volltrefferquote an. <PERSON> schreckt eventuell zurück."}, "transform": {"name": "<PERSON><PERSON>", "effect": "<PERSON><PERSON>der verwandelt sich in ein Abbild des Zieles und kann so auf die gleichen Attacken zugreifen."}, "bubble": {"name": "Blu<PERSON>r", "effect": "Angriff mit Blasen. Initiative-Wert des Zieles wird eventuell gesenkt."}, "dizzyPunch": {"name": "Irrschlag", "effect": "Rhythmische Schläge, die das Ziel verwirren können."}, "spore": {"name": "Pilzspore", "effect": "<PERSON><PERSON><PERSON><PERSON> eine Wolke aus einschläfernden Sporen."}, "flash": {"name": "Blitz", "effect": "<PERSON><PERSON><PERSON><PERSON> hell<PERSON> Licht, das die Genauigkeit des Zieles senkt."}, "psywave": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON>der erzeugt eine mysteriöse Energiewelle, deren Intensität von Mal zu Mal anders ausfällt."}, "splash": {"name": "<PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON> e<PERSON>, der überhaupt nichts bewirkt."}, "acidArmor": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Verflüssigt Körperzellen des Anwenders. Erhöht den Verteidigungs-Wert stark."}, "crabhammer": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON><PERSON> mit Schere zu. Hohe Volltrefferquote."}, "explosion": {"name": "Explosion", "effect": "<PERSON><PERSON><PERSON> explodiert, richtet bei allen Pokémon in seiner Umgebung großen Schaden an und wird selbst kampfunfähig."}, "furySwipes": {"name": "Kratzfurie", "effect": "Beharkt das Ziel zwei- bis fünfmal mit scharfen Klauen oder Sicheln."}, "bonemerang": {"name": "Knochmerang", "effect": "Ein Bumerang aus Knochen, der zweimal trifft."}, "rest": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Anwender wird vollkommen geheilt und schläft die folgenden zwei Runden."}, "rockSlide": {"name": "<PERSON><PERSON><PERSON>", "effect": "Schleudert riesige Felsen auf Ziele in der Umgebung, die eventuell zurückschrecken."}, "hyperFang": {"name": "Hyperzahn", "effect": "Angriff mit scharfen Reißzähnen. Z<PERSON> schreckt eventuell zurück."}, "sharpen": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON>der senkt die Polygonzahl, um Kanten zu erzeugen, die den Angriffs-Wert erhöhen."}, "conversion": {"name": "Umwandlung", "effect": "Wandelt den Typ des Anwenders in den Typ der ersten Attacke des Anwenders um."}, "triAttack": {"name": "Triplette", "effect": "Feuert drei Strahlen ab. Verursacht eventuell Paralyse, Verbrennung oder Einfrieren."}, "superFang": {"name": "Superzahn", "effect": "Greift mit scharfen Reißzähnen an. KP des Zieles werden halbiert."}, "slash": {"name": "Schlitzer", "effect": "Hieb mit Klauen oder Ähnlichem. Hohe Volltrefferquote."}, "substitute": {"name": "Delegator", "effect": "<PERSON><PERSON>der setzt eine kleine Menge an KP ein, um einen Doppelgänger zu erzeugen, der für ihn Schläge einsteckt."}, "struggle": {"name": "Verzweifler", "effect": "Angriff nur bei verbrauchten AP. Anwender verletzt sich selbst leicht."}, "sketch": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON>der lernt die letzte Attacke des Zieles dauerhaft. Nachahmer verschwindet nach Gebrauch."}, "tripleKick": {"name": "Dreifachkick", "effect": "Tritt das Ziel ein- bis dreimal nacheinander. Die Härte der Tritte nimmt von Treffer zu Treffer zu."}, "thief": {"name": "<PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON><PERSON> es, das Item des Zieles zu stehlen, solang der Anwender selbst keins bei sich trägt."}, "spiderWeb": {"name": "Spinnennetz", "effect": "Wickelt das Ziel ein. Flucht oder Tausch unmöglich."}, "mindReader": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Ahnt Bewegungen des Zieles voraus, um zu gewährleisten, dass die nächste eigene Attacke trifft."}, "nightmare": {"name": "Nachtmahr", "effect": "Dem schlafenden Ziel wird durch einen Alptraum in jeder Runde Schaden zugefügt, solang es schläft."}, "flameWheel": {"name": "Flammenrad", "effect": "<PERSON><PERSON><PERSON><PERSON><PERSON>, die das Ziel eventuell verbrennt."}, "snore": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Attacke nur im Schlaf möglich. Ziel schreckt eventuell zurück."}, "curse": {"name": "<PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON>, deren W<PERSON> davon a<PERSON>, ob der Anwender ein Geist-Pokémon ist."}, "flail": {"name": "Dreschflegel", "effect": "<PERSON>e richtet mehr Schaden an, wenn eigene KP niedrig sind."}, "conversion2": {"name": "Umwandlung2", "effect": "Anwender ändert Typ und wird gegen letzten Angriffstyp resistent."}, "aeroblast": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON><PERSON>trudel gegen das Ziel. Hohe Volltrefferquote."}, "cottonSpore": {"name": "Baumwollsaat", "effect": "Wattebäusche heften sich an das Ziel. Der Initiative-Wert sinkt stark."}, "reversal": {"name": "Gegenschlag", "effect": "Richtet mehr Schaden an, wenn eigene KP niedrig sind."}, "spite": {"name": "<PERSON><PERSON>", "effect": "AP der letzten Attacke des Zieles werden um 4 gesenkt."}, "powderSnow": {"name": "Pulverschnee", "effect": "Angriff mit Schnee. Das Ziel wird eventuell eingefroren."}, "protect": {"name": "Schutzschild", "effect": "Anwender weicht jeder Attacke aus. Scheitert eventuell bei Wiederholung."}, "machPunch": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Extrem schneller Hieb, der stets zuerst trifft."}, "scaryFace": {"name": "Grimasse", "effect": "J<PERSON>t dem Ziel mit einer Grimasse Angst ein. Dessen Initiative-<PERSON>rt sinkt stark."}, "feintAttack": {"name": "Finte", "effect": "<PERSON><PERSON>der nähert sich mit Unschuldsmiene dem Ziel und schlägt zu, sobald dieses unachtsam wird. Ein Treffer ist gewiss."}, "sweetKiss": {"name": "Bitterkuss", "effect": "Anwen<PERSON> küsst das Ziel, das durch diese Niedlichkeit verwirrt wird."}, "bellyDrum": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender maximiert den Angriffs-Wert auf Kosten der Hälfte seiner maximalen KP."}, "sludgeBomb": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Wirft Schlamm auf das Ziel. Dieses wird eventuell vergiftet."}, "mudSlap": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Schadet dem Ziel durch Matsch. Dessen Genauigkeit sinkt."}, "octazooka": {"name": "Octazooka", "effect": "<PERSON><PERSON><PERSON><PERSON>t mit Tin<PERSON>, um Schaden anzurichten und die Genauigkeit zu senken."}, "spikes": {"name": "<PERSON><PERSON><PERSON>", "effect": "Der Anwender verteilt Stacheln, die gegnerische Pokémon verletzen, die in den Kampf gerufen werden."}, "zapCannon": {"name": "Blitzkanone", "effect": "Kanonenähnlicher Elektro-Schuss, der schadet und paralysiert."}, "foresight": {"name": "Scharfblick", "effect": "Erlaubt es, Geist-Pokémon mit Normal- und Kampf-Attacken anzugreifen. Ignoriert den Fluchtwert des Zieles."}, "destinyBond": {"name": "Abgangsbund", "effect": "Wird der Anwender nach Einsatz dieser Attacke besiegt, führt dies auch beim Ziel zum K.O."}, "perishSong": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON>r diese Musik hört, wird nach drei Runden besiegt. Rettung ist durch den Eintausch eines neuen Pokémon möglich."}, "icyWind": {"name": "Eissturm", "effect": "<PERSON><PERSON><PERSON><PERSON><PERSON>, die dem Ziel Schaden zufügt und seinen Initiative-<PERSON><PERSON> senkt."}, "detect": {"name": "Scanner", "effect": "Anwender weicht jeder Attacke aus. Scheitert eventuell bei Wiederholung."}, "boneRush": {"name": "Knochenhatz", "effect": "Greift Ziel zwei- bis fünfmal in Folge mit einem harten Knochen an."}, "lockOn": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Visiert das Ziel an und trifft in der nächsten Runde garantiert."}, "outrage": {"name": "Wutanfall", "effect": "Attacke über zwei bis drei Runden, die den Anwender verwirrt."}, "sandstorm": {"name": "Sandsturm", "effect": "Sandsturm für fünf Runden. <PERSON><PERSON><PERSON> <PERSON> von jedem <PERSON> auße<PERSON>, Boden und Stahl Schaden zu."}, "gigaDrain": {"name": "Gigasauger", "effect": "Das Ziel wird angegriffen und die Hälfte des zugefügten Schadens dem Angreifer als KP gutgeschrieben."}, "endure": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Nach fatalen Attacken bleibt stets 1 KP übrig. Misserfolg bei Wiederholung möglich."}, "charm": {"name": "Charm<PERSON>", "effect": "Betört das Ziel und reduziert dessen Angriffs-<PERSON>rt stark."}, "rollout": {"name": "<PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON>, die fünf Runden dauert. Die Härte nimmt von Mal zu Mal zu."}, "falseSwipe": {"name": "Trugschlag", "effect": "Ein Angriff, der dem Ziel zumindest 1 KP lässt."}, "swagger": {"name": "Angeb<PERSON><PERSON>", "effect": "Verwirrt das Ziel und erhöht dessen Angriffs-<PERSON>rt stark."}, "milkDrink": {"name": "Mil<PERSON>geträ<PERSON>", "effect": "KP des Anwenders werden um 50 % der maximalen KP aufgefüllt."}, "spark": {"name": "Funkensprung", "effect": "Elektro-<PERSON><PERSON>, der das Ziel paralysieren kann."}, "furyCutter": {"name": "Zornklinge", "effect": "Eine Attacke mit Scheren oder Klauen, deren Härte bei aufeinanderfolgenden Treffern zunimmt."}, "steelWing": {"name": "Stahlflügel", "effect": "Trifft das Ziel mit Stahlflügeln. Verteidigungs-Wert des Anwenders steigt eventuell."}, "meanLook": {"name": "Horrorblick", "effect": "<PERSON><PERSON><PERSON>, der die Flucht des Zieles vereitelt."}, "attract": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Angriff auf Anwender vom anderen Geschlecht wird unwahrscheinlich."}, "sleepTalk": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON> setzt per Z<PERSON>all eine ihm bekannte Attacke im Schlaf ein."}, "healBell": {"name": "Vitalglocke", "effect": "Läutet beruhigend und heilt alle Statusprobleme im Team."}, "return": {"name": "Rückkehr", "effect": "<PERSON><PERSON>, dessen Kraft bei Freundschaft zum Trainer größer wird."}, "present": {"name": "Geschenk", "effect": "Eine Bombe als Geschenk. Kann auch KP des Zieles wiederherstellen."}, "frustration": {"name": "Frustration", "effect": "Die Attacke wird st<PERSON>er, je weniger der Anwender seinen Trainer mag."}, "safeguard": {"name": "Bodyguard", "effect": "Team des Anwenders ist fünf Runden vor Statusproblemen geschützt."}, "painSplit": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON>t K<PERSON> von <PERSON> und Ziel. Te<PERSON> sie gerecht auf."}, "sacredFire": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Mystische Feuer-Attacke, durch die das Ziel eventuell Verbrennungen erleidet."}, "magnitude": {"name": "Intensität", "effect": "<PERSON>rdbebenart<PERSON> Angriff von zufälliger Stärke gegen andere Pokémon in der Umgebung des Anwenders."}, "dynamicPunch": {"name": "Wuchtschlag", "effect": "Kräftiger Schlag, der das Ziel bei Erfolg verwirrt."}, "megahorn": {"name": "<PERSON><PERSON><PERSON>", "effect": "<PERSON>ru<PERSON>r Ramm-<PERSON><PERSON> mit spitzem, beeindruckendem Horn."}, "dragonBreath": {"name": "Feuerodem", "effect": "Fegt das Ziel mit zerstörerisch heißem Atem weg. Paralysiert das Ziel eventuell."}, "batonPass": {"name": "Stafette", "effect": "Tauscht das eigene Pokémon aus. Alle Statusveränderungen bleiben bestehen."}, "encore": {"name": "Zugabe", "effect": "Das Ziel wiederholt die letzte Attacke drei Runden lang."}, "pursuit": {"name": "Verfolgung", "effect": "Die Attacke richtet beim Ziel doppelten Schaden an, falls es ausgetauscht wird."}, "rapidSpin": {"name": "<PERSON><PERSON><PERSON>", "effect": "Trifft das Ziel mit einer Dreh-Attacke. Befreit sich unter and<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Egelsamen und Stachler."}, "sweetScent": {"name": "Lockduft", "effect": "Lockt Ziele an und senkt deren Fluchtwert. Lockt im Gras auch wilde Pokémon an."}, "ironTail": {"name": "<PERSON>isenschweif", "effect": "Attacke mit hartem Eisenschweif. Senkt eventuell den Verteidigungs-Wert des Zieles."}, "metalClaw": {"name": "Metallklaue", "effect": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>, die eventuell den Angriffs-Wert des Anwenders erhöht."}, "vitalThrow": {"name": "Überwurf", "effect": "<PERSON><PERSON>der greift als Letzter an, hat dafür aber eine Treffergarantie beim eigenen Angriff."}, "morningSun": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Füllt KP des Anwenders auf. Die Menge hängt vom Wetter ab."}, "synthesis": {"name": "Synthese", "effect": "Füllt KP des Anwenders auf. Die Menge hängt vom Wetter ab."}, "moonlight": {"name": "Mondschein", "effect": "Füllt KP des Anwenders auf. Die Menge hängt vom Wetter ab."}, "hiddenPower": {"name": "Kraftreserve", "effect": "Wirkung und Typ der Attacke hängen vom Anwender ab."}, "crossChop": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON> Hieb mit den Unterarmen. Hohe Volltrefferquote."}, "twister": {"name": "Windhose", "effect": "<PERSON><PERSON><PERSON> Z<PERSON> in der Umgebung mit einem heftigen Wirbelsturm, was diese eventuell zurückschrecken lässt."}, "rainDance": {"name": "<PERSON><PERSON><PERSON>", "effect": "Anwender erzeugt starken Regen. Die Stärke von Wasser-Attacken erhöht sich fünf Runden lang."}, "sunnyDay": {"name": "Sonnentag", "effect": "Die Sonne brennt unbarmherzig fünf Runden lang. Dadurch werden Attacken vom Typ Feuer verstärkt."}, "crunch": {"name": "<PERSON><PERSON><PERSON>", "effect": "Beißt mit scharfen Reißzähnen zu und senkt eventuell die Verteidigung."}, "mirrorCoat": {"name": "Spiegelcape", "effect": "Kontert den Spezial-Angriff des Gegners mit doppeltem Schaden."}, "psychUp": {"name": "Psycho-Plus", "effect": "Der Anwender hypnotisiert sich selbst, um die Statusveränderungen des Zieles zu kopieren."}, "extremeSpeed": {"name": "Turbotempo", "effect": "Extrem schnelle und kraftvolle Attacke, die stets zu<PERSON>t trifft."}, "ancientPower": {"name": "Antik-Kraft", "effect": "Angriff mit antiker Kraft, der alle Statuswerte erhöhen kann."}, "shadowBall": {"name": "Spukball", "effect": "Bewirft das Ziel mit gruseligem Ball und senkt eventuell die Spezial-Verteidigung."}, "futureSight": {"name": "<PERSON><PERSON>", "effect": "<PERSON><PERSON>, nachdem Se<PERSON> e<PERSON> wurde, erfo<PERSON><PERSON> der Angriff."}, "rockSmash": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Diese steinbrechende Attacke kann den Verteidigungs-Wert des Zieles senken und außer<PERSON>b von Kämpfen rissige Felsen zertrümmern."}, "whirlpool": {"name": "Whirlpool", "effect": "Das Ziel wird für vier bis fünf Runden in einer Wasserhose gefangen."}, "beatUp": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Das gesamte Team nimmt aktiv am Kampf teil. Je mehr Pokémon, desto höher die Anzahl der Angriffe."}, "fakeOut": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Diese Attacke trifft zuerst. Das Ziel schreckt zurück. Gelin<PERSON> nur in der ersten Runde eines Kampfes."}, "uproar": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Anwender greift an, indem er über drei Runden hinweg einen Aufruhr erzeugt. Verhindert Schlaf."}, "stockpile": {"name": "Horter", "effect": "Lädt Kraft für später auf. Erhöht Verteidigung und Spezial-Verteidigung. Kann bis zu dreimal eingesetzt werden."}, "spitUp": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON><PERSON> die Kraft, die während des Einsatzes von Horter gesammelt wurde."}, "swallow": {"name": "V<PERSON>zehr<PERSON>", "effect": "Absorbiert die gehortete Kraft, um KP aufzufüllen."}, "heatWave": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON>iele werden von Sturm aus heißer Luft getroffen und verbrennen sich eventuell."}, "hail": {"name": "Hagelsturm", "effect": "Hagelsturm für fünf Runden. Schadet allen, außer <PERSON>-Pokémon."}, "torment": {"name": "Folterknecht", "effect": "<PERSON><PERSON><PERSON><PERSON><PERSON> das Ziel, um wiederholten Einsatz derselben Attacke zu verhindern."}, "flatter": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Schmeichelt dem Ziel, um es zu verwirren. Erhöht dessen Spezial-Angriff."}, "willOWisp": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Angriff mit unheimlicher Flamme, die das Ziel verbrennt."}, "memento": {"name": "Memento-<PERSON><PERSON>", "effect": "Der Anwender wird besiegt und senkt den Angriffs-Wert und den Spezial-Angriff des Zieles stark."}, "facade": {"name": "Fassade", "effect": "Doppelte Stärke nach Verbrennung, Paralyse oder Vergiftung."}, "focusPunch": {"name": "Power-Punch", "effect": "<PERSON>wender konzen<PERSON>ert sich, bevor er angreift. Wird er vorher getroffen, ist die Attacke erfolglos."}, "smellingSalts": {"name": "Riechsalz", "effect": "<PERSON><PERSON>t wirksam gegen paralysierte Ziele, heilt sie aber auch von der Paralyse."}, "followMe": {"name": "Spotlight", "effect": "<PERSON>ieht Aufmerksamkeit auf sich. Gegner greift nur Anwender an."}, "naturePower": {"name": "Natur-Kraft", "effect": "Angriff mit der Kraft der Natur, dessen Typ vom Ort abhängt, wo er durchgeführt wird."}, "charge": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Lädt Energie für die kommende Elektro-Attacke auf. Erhöht die Spezial-Verteidigung."}, "taunt": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Bringt das Ziel in Rage. Dieses kann über drei Runden hinweg nur noch angreifen."}, "helpingHand": {"name": "<PERSON><PERSON><PERSON>", "effect": "An<PERSON><PERSON> steigert die Kraft eines Angriffes eines Freundes."}, "trick": {"name": "Trickbetrug", "effect": "Der Anwender überrumpelt das Ziel und tauscht mit ihm die getragenen Items."}, "rolePlay": {"name": "Rollenspiel", "effect": "Parodiert das Ziel und kopiert seine Fähigkeit."}, "wish": {"name": "Wunschtraum", "effect": "Ein Wunsch füllt in der nächsten Runde 50 % der KP des Anwenders bei diesem oder einem eingewechselten Pokémon auf."}, "assist": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "G<PERSON><PERSON> zufällig mit einer Attacke eines Mitstreiters an."}, "ingrain": {"name": "Verwurzler", "effect": "Verwurzelung füllt jede Runde KP auf. Austausch ist unmöglich."}, "superpower": {"name": "Kraftkoloss", "effect": "<PERSON><PERSON>, die jedoch auch den Angriff und die Verteidigung des Anwenders senkt."}, "magicCoat": {"name": "Magiemantel", "effect": "Egelsamen und alle Attacken mit Status verändernden Effekten prallen ab."}, "recycle": {"name": "Aufbereitung", "effect": "Recycling eines getragenen Items, das zuvor im Kampf verwendet wurde."}, "revenge": {"name": "Vergeltung", "effect": "<PERSON><PERSON><PERSON> verdoppelt sich, wenn der Anwender in der Runde bereits Schaden vom Ziel des Angriffes genommen hat."}, "brickBreak": {"name": "Durchbruch", "effect": "Ein beherzter Handkantenschlag. Durchbricht Barrieren wie Lichtschild und Reflektor."}, "yawn": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON> g<PERSON> und das Ziel schläft in der nächsten Runde ein."}, "knockOff": {"name": "Abschlag", "effect": "<PERSON><PERSON><PERSON>gt das Item des Zieles weg und vereitelt so dessen Gebrauch während des Kampfes. Mehr Schaden gegen Ziele, die ein Item bei sich tragen."}, "endeavor": {"name": "Notsituation", "effect": "<PERSON><PERSON>t nur, wenn KP des Anwenders geringer als KP des Zieles sind. Senkt dessen KP auf die Höhe der KP des Anwenders."}, "eruption": {"name": "Eruption", "effect": "Explosiver Angriff. Je höher die KP des Anwenders sind, desto mehr Schaden wird angerichtet."}, "skillSwap": {"name": "Fähigkeitstausch", "effect": "Anwender tauscht Fähigkeit mit dem Ziel."}, "imprison": {"name": "Begrenzer", "effect": "Hindert Gegner am Einsatz von Attacken, die der Anwender selbst auch kennt."}, "refresh": {"name": "Heilung", "effect": "Selbstheilung bei Vergiftung, Paralyse und Verbrennung."}, "grudge": {"name": "Nachspiel", "effect": "Bei K.O. des Anwenders werden die AP der Attacke, durch die er besiegt wurde, auf 0 herabgesetzt."}, "snatch": {"name": "Übernahme", "effect": "Raubt den Effekt eingesetzter heilender oder Werte verändernder Attacken."}, "secretPower": {"name": "Geheimpower", "effect": "<PERSON><PERSON>, der abhängig vom Anwendungsort einen unterschiedlichen Zusatz-Effekt hat."}, "dive": {"name": "<PERSON><PERSON>", "effect": "Taucht in Runde 1 ab und greift in Runde 2 aus der Tiefe an."}, "armThrust": {"name": "<PERSON><PERSON><PERSON>", "effect": "Schläge mit geradem Arm, die das Ziel zwei- bis fünfmal treffen."}, "camouflage": {"name": "Tarnung", "effect": "Der Typ des Anwenders passt sich der Umgebung an, sei es im Wasser, im Gras oder in einer Höhle."}, "tailGlow": {"name": "Schweifglanz", "effect": "Ein blinkendes Licht, das den Spezial-Angriff drastisch erhöht."}, "lusterPurge": {"name": "Scheinwerfer", "effect": "Angriff mit grellem Licht, der die Spezial-Verteidigung des Zieles eventuell senkt."}, "mistBall": {"name": "Nebelball", "effect": "Angriff mit einer Kugel aus Wasser, die Nebel enthält. Senkt eventuell den Spezial-Angriff des Zieles."}, "featherDance": {"name": "Daunenreigen", "effect": "Hüllt das Ziel in Daunen und senkt dessen Angriffs-<PERSON><PERSON> stark."}, "teeterDance": {"name": "Taumeltanz", "effect": "<PERSON> Wackeltanz, der andere Pokémon in der Umgebung des Anwenders verwirrt."}, "blazeKick": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON>er Tritt mit hoher Volltrefferquote. Verursacht eventuell Verbrennung."}, "mudSport": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Schwächt Elektro-Attacken, solang der Anwender am Kampf teilnimmt."}, "iceBall": {"name": "Frostbeule", "effect": "<PERSON><PERSON>, die fünf Runden dauert. Die Härte nimmt von Mal zu Mal zu."}, "needleArm": {"name": "Nietenranke", "effect": "Angriff mit dornigen Armen. Das Ziel schreckt eventuell zurück."}, "slackOff": {"name": "<PERSON>edieb", "effect": "Durch Müßiggang werden KP des Anwenders um 50 % der maximalen KP aufgefüllt."}, "hyperVoice": {"name": "Schallwelle", "effect": "Laute Attacke mit Schallwellen."}, "poisonFang": {"name": "<PERSON><PERSON><PERSON>", "effect": "Angriff mit giftigen Reißzähnen. Das Ziel wird eventuell schwer vergiftet."}, "crushClaw": {"name": "Zermalmklaue", "effect": "Angriff mit scharfen Klauen. Senkt eventuell den Verteidigungs-Wert."}, "blastBurn": {"name": "Lohekanonade", "effect": "Das Ziel wird von starker Explosion getroffen. <PERSON><PERSON><PERSON> setzt eine Runde aus."}, "hydroCannon": {"name": "Aquahaubitze", "effect": "Das Ziel wird von Wasserkanone getroffen. <PERSON><PERSON><PERSON> setzt eine Runde aus."}, "meteorMash": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Angriff mit einem harten, schnellen Schlag. Erhöht eventuell Angriffs-Wert des Anwenders."}, "astonish": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON> greift mit einem Schrei an. Ein Angriff, der das Ziel eventuell zurückschrecken lässt."}, "weatherBall": {"name": "Meteorologe", "effect": "Typ und Stärke der Attacke sind vom Wetter zum Zeitpunkt der Anwendung abhängig."}, "aromatherapy": {"name": "Aromakur", "effect": "Heilt alle Statusprobleme des Teams mit beruhigendem Duft."}, "fakeTears": {"name": "Trugträne", "effect": "T<PERSON>uscht Weinen vor, um die Spezial-Verteidigung des Zieles stark zu senken."}, "airCutter": {"name": "Windschnitt", "effect": "Greift mit rasierklingenartigem Wind an. Hohe Volltrefferquote."}, "overheat": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Angriff mit voller Kraft, der den Spezial-Angriff des Anwenders durch den Rückstoß stark senkt."}, "odorSleuth": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Erlaubt es, Geist-Pokémon mit Normal- und Kampf-Attacken anzugreifen. Ignoriert den Fluchtwert des Zieles."}, "rockTomb": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Angriff mit Felsen. Bei Erfolg wird der Initiative-Wert des Zieles gesenkt."}, "silverWind": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Angriff mit Silberstaub. Eventuell werden alle Statuswerte des Anwenders erhöht."}, "metalSound": {"name": "Metallsound", "effect": "<PERSON><PERSON><PERSON><PERSON> einen spitzen Schrei aus, der die Spezial-Verteidigung des Zieles stark senkt."}, "grassWhistle": {"name": "Grasflöte", "effect": "Versetzt das Ziel durch eine schöne Melodie in Tiefschlaf."}, "tickle": {"name": "Spaßkanone", "effect": "Bringt das Ziel zum Lachen und senkt dadurch dessen Angriff und Verteidigung."}, "cosmicPower": {"name": "Kosmik-Kraft", "effect": "Erhöht Verteidigung und Spezial-Verteidigung durch eine mystische Kraft."}, "waterSpout": {"name": "Fontränen", "effect": "<PERSON><PERSON><PERSON><PERSON><PERSON>, die wirkungsvoller ist, wenn KP des Anwenders hoch sind."}, "signalBeam": {"name": "Ampelleuchte", "effect": "<PERSON><PERSON><PERSON><PERSON><PERSON>, die das Ziel eventuell verwirrt."}, "shadowPunch": {"name": "Finsterfaust", "effect": "Angriff mit der Faust aus dem Schattenreich. Ausweichen unmöglich."}, "extrasensory": {"name": "Sondersensor", "effect": "Besonderer Angriff mit einer unsichtbaren Kraft, die das Ziel eventuell zurückschrecken lässt."}, "skyUppercut": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON><PERSON>, der das Ziel gen Himmel schickt."}, "sandTomb": {"name": "<PERSON><PERSON><PERSON>", "effect": "Das Ziel leidet für vier bis fünf Runden in einer Sandhose."}, "sheerCold": {"name": "Eiseskälte", "effect": "<PERSON><PERSON> mit Kälte, die das Ziel bei Erfolg besiegt."}, "muddyWater": {"name": "Lehmbrühe", "effect": "Greift mit Matsch an und senkt eventuell die Genauigkeit des Zieles."}, "bulletSeed": {"name": "Kugelsaat", "effect": "Der Anwender wirft zwei- bis fünfmal in rascher Folge Samen auf das Ziel."}, "aerialAce": {"name": "Aero-Ass", "effect": "Eine extrem schnelle Attacke, der das Ziel nicht ausweichen kann."}, "icicleSpear": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Feuert zwei bis fünf Eiszapfen auf das Ziel."}, "ironDefense": {"name": "Eisenabwehr", "effect": "Anwender stärkt den Körper, um den Verteidigungs-Wert stark zu erhöhen."}, "block": {"name": "Rückentzug", "effect": "Anwender versperrt den Fluchtweg des Zieles."}, "howl": {"name": "<PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON>, um seinen Kampfgeist und seinen Angriffs-<PERSON>rt zu erhöhen."}, "dragonClaw": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Das Ziel wird mit riesigen, scharfen Klauen stark verletzt."}, "frenzyPlant": {"name": "Flora-Statue", "effect": "Angriff mit dickem Ast. Der Angreifer muss eine Runde aussetzen."}, "bulkUp": {"name": "<PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON> den Körper auf, um den Angriff und die Verteidigung zu erhöhen."}, "bounce": {"name": "Sprungfeder", "effect": "<PERSON><PERSON><PERSON> springt und landet in der nächsten Runde auf dem Ziel. Das Ziel wird eventuell paralysiert."}, "mudShot": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON> mit Lehm, der den Initiative-Wert des Zieles senkt."}, "poisonTail": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Angriff mit hoher Volltrefferquote. Diese Schweifattacke vergiftet das Ziel eventuell."}, "covet": {"name": "Be<PERSON>rzer", "effect": "Bittet charmant um das getragene Item des Zieles und stiehlt es dann."}, "voltTackle": {"name": "Volttackle", "effect": "Angriff mit Elektro-Tackle. Der Anwender verletzt sich dabei. Das Ziel wird eventuell paralysiert."}, "magicalLeaf": {"name": "Zauberblatt", "effect": "Magis<PERSON> Blattangriff, dem nicht auszuweichen ist."}, "waterSport": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender lässt Wasser herabregnen und schwächt damit fünf Runden lang Feuer-Attacken."}, "calmMind": {"name": "Gedankengut", "effect": "Erhöht Spezial-Angriff und Spezial-Verteidigung durch Konzentration."}, "leafBlade": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Hieb mit scharfkantigem Blatt. Hohe Volltrefferquote."}, "dragonDance": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Ein mystischer Tanz, der den Angriffs- und Initiative-Wert erhöht."}, "rockBlast": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Wirft zwei- bis fünfmal in Folge Felsblöcke auf das Ziel."}, "shockWave": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Angriff mit schnellem Elektro-Schlag. Ausweichen nicht möglich."}, "waterPulse": {"name": "Aquawelle", "effect": "<PERSON><PERSON> mit Wasserwelle, die das Ziel eventuell verwirren kann."}, "doomDesire": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Angriff mit gebündeltem Licht erfolgt zwei Runden nach Attackeneinsatz."}, "psychoBoost": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Angriff mit voller Kraft, der den Spezial-Angriff des Anwenders durch den Rückstoß stark senkt."}, "roost": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Anwender landet und ruht sich aus. KP des Anwenders werden um 50 % der maximalen KP aufgefüllt."}, "gravity": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Die Gravitation wird für fünf Runden erhöht. Macht Fliegen unmöglich und verhindert Schwebe."}, "miracleEye": {"name": "Wunderauge", "effect": "Erl<PERSON>bt es, Unlicht-Pokémon mit Psycho-Attacken anzugreifen. Ignoriert den Fluchtwert des Zieles."}, "wakeUpSlap": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Richtet großen Schaden bei einem schlafenden Ziel an, weckt es aber auch auf."}, "hammerArm": {"name": "Hammerarm", "effect": "Anwender trifft mit einem starken Hieb. Senkt Initiative des Anwenders."}, "gyroBall": {"name": "Gyroball", "effect": "Angriff mit hoher Geschwindigkeit. Je niedriger die Initiative des Anwenders, desto höher der Schaden."}, "healingWish": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON> geht K.O. Das an seine Stelle tretende Pokémon hat volle KP. Statusprobleme werden geheilt."}, "brine": {"name": "Lake", "effect": "Hat das Ziel die Hälfte oder weniger seiner maximalen KP, trifft diese Attacke mit doppelter Kraft."}, "naturalGift": {"name": "Beerenkräfte", "effect": "Anwender zieht aus seiner derzeitigen Beere Kraft. Sie bestimmt Typ und Stärke der Attacke."}, "feint": {"name": "Offenlegung", "effect": "<PERSON><PERSON><PERSON>, die Schutzschild oder Scanner verwenden, werden getroffen. Entfernt Effekte dieser Attacken."}, "pluck": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON> pickt das Ziel, nimmt die Beere, falls das Ziel eine trägt, und erhält ihren Effekt."}, "tailwind": {"name": "Rückenwind", "effect": "<PERSON><PERSON>der erzeugt einen Wirbelwind, der die Initiative aller Pokémon im Team für vier Runden steigert."}, "acupressure": {"name": "Akupressur", "effect": "Anwender erhöht Druck auf Stresspunkte und steigert einen Statuswert stark."}, "metalBurst": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Attacke mit großer Kraft gegen das Ziel, das dem Anwender in derselben Runde zuletzt Schaden zufügte."}, "uTurn": {"name": "Kehrtwende", "effect": "Nach der Attacke eilt der Anwender zurück und tauscht den Platz mit einem anderen Pokémon."}, "closeCombat": {"name": "Nahkampf", "effect": "Nahkampf-Attacke ohne Rücksicht auf Verluste. Senkt Verteidigung und Spezial-Verteidigung des Anwenders."}, "payback": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender lädt die Attacke auf. Handelt das Ziel vor dem Anwender, verdoppelt sich die Kraft der Attacke."}, "assurance": {"name": "Gewissheit", "effect": "Hat das Ziel während der Runde schon Schaden genommen, wird die Kraft der Attacke verdoppelt."}, "embargo": {"name": "Itemsperre", "effect": "<PERSON><PERSON><PERSON><PERSON><PERSON>, dass auf das Ziel Items verwendet werden."}, "fling": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "An<PERSON><PERSON> schleudert sein Item auf das Ziel. Kraft und Effekt der Attacke hängen vom Item ab."}, "psychoShift": {"name": "Psybann", "effect": "<PERSON><PERSON>der nutzt seine Suggestivkräfte, um eigene Statusprobleme auf das Ziel zu transferieren."}, "trumpCard": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON> weniger AP diese Attacke hat, desto mehr Angriffskraft besitzt sie."}, "healBlock": {"name": "Heil<PERSON><PERSON>", "effect": "Anwender verhindert für fünf Runden, dass Ziele durch Attacken, Fähigkeiten oder Items KP regenerieren."}, "wringOut": {"name": "Auswringen", "effect": "Anwen<PERSON> presst sein Ziel aus. Je höher die KP des Zieles, desto kraftvoller die Attacke."}, "powerTrick": {"name": "Krafttrick", "effect": "<PERSON><PERSON><PERSON> setzt Psycho-Kräfte ein, um eigenen Angriffs- mit Verteidigungs-Wert auszutauschen."}, "gastroAcid": {"name": "Magensäfte", "effect": "An<PERSON>der greift das Ziel mit eigenen Magensäften an. Entfernt Effekte von dessen Fähigkeit."}, "luckyChant": {"name": "Beschwörung", "effect": "<PERSON><PERSON><PERSON> singt eine Beschwörungsformel, die Volltreffer gegen ihn verhindert."}, "meFirst": {"name": "E<PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON> stiehlt und führt die Attacke eines langsameren Zieles zu<PERSON>t und mit größerer Kraft aus."}, "copycat": {"name": "Imitator", "effect": "<PERSON><PERSON><PERSON> imitiert gerade verwendete Attacke. Die<PERSON> schl<PERSON>gt fehl, falls zuvor keine Attacke verwendet wurde."}, "powerSwap": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Psychische Kräfte tauschen Änderungen an Angriff und Spezial-Angriff mit denen des Zieles."}, "guardSwap": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Psychische Kräfte tauschen Änderungen an Verteidigung und Spezial-Verteidigung mit denen des Zieles."}, "punishment": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Je stärker das Ziel durch Statusveränderungen ist, desto stärker wirkt diese Attacke."}, "lastResort": {"name": "Zuflucht", "effect": "Diese Attacke kann nur eingesetzt werden, nachdem alle verfügbaren Attacken ausgeführt worden sind."}, "worrySeed": {"name": "Sorgensamen", "effect": "Ziel wird bepflanzt. Wandelt Fähigkeit in Insomnia um. Verhindert so Schlaf."}, "suckerPunch": {"name": "Tiefschlag", "effect": "Ermöglicht den Erstschlag. Gelingt aber nur, wenn das Ziel gerade eine Attacke vorbereitet."}, "toxicSpikes": {"name": "Giftspitzen", "effect": "Anwender legt eine Falle mit Giftdornen aus. In den Kampf eingewechselte gegnerische Pokémon werden vergiftet."}, "heartSwap": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON> setzt Psycho-Kräfte ein, um Statusveränderungen des Zieles mit den eigenen zu tauschen."}, "aquaRing": {"name": "Wasserring", "effect": "An<PERSON>der umgibt sich mit einem Schleier aus Wasser. Dabei regeneriert er einige KP pro Runde."}, "magnetRise": {"name": "Magnetflug", "effect": "Anwender schwebt für fünf Runden durch elektrisch erzeugten Magnetismus."}, "flareBlitz": {"name": "Flammenblitz", "effect": "Anwender hüllt sich in Flammen und stürmt auf das Ziel zu, das sich eventuell verbrennt. Anwender nimmt selbst großen Schaden."}, "forcePalm": {"name": "Kraftwelle", "effect": "Das Ziel wird mit einer Schockwelle angegriffen, die es eventuell paralysiert."}, "auraSphere": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Tief aus dem Inneren des Anwenders löst sich ein kraftvoller Stoß Auraenergie. Trifft in jedem Fall."}, "rockPolish": {"name": "Steinpolitur", "effect": "<PERSON><PERSON><PERSON> reduziert so gut wie möglich den Luftwiderstand. Kann Initiative-<PERSON>rt stark steigern."}, "poisonJab": {"name": "<PERSON><PERSON><PERSON>", "effect": "Ziel wird mit vergiftetem Arm oder Tentakel verletzt. Es wird dabei eventuell vergiftet."}, "darkPulse": {"name": "Finsteraura", "effect": "Anwender greift mit fürchterlicher Aura schlechter Gedanken an. <PERSON><PERSON> schreckt eventuell zurück."}, "nightSlash": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Anwender greift bei der ersten Gelegenheit mit scharfen Klauen an. Hohe Volltrefferquote."}, "aquaTail": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON> attackiert mit dem Schweif, als ob dieser eine brutale Welle in einem tosenden Sturm sei."}, "seedBomb": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON>der lässt eine Menge Samen mit harter Schale von oben auf das Ziel fallen."}, "airSlash": {"name": "Luftschnitt", "effect": "Das Ziel wird mit einer Luftklinge angegriffen. Z<PERSON> schreckt eventuell zurück."}, "xScissor": {"name": "Kreuzschere", "effect": "Der Anwender führt eine Attacke aus, die einer Scherenbewegung ähnelt."}, "bugBuzz": {"name": "Käfergebrumm", "effect": "<PERSON><PERSON><PERSON> schlägt mit den Flügeln und erzeugt eine Schockwelle. Senkt eventuell Spezial-Verteidigung des Zieles."}, "dragonPulse": {"name": "Drachenpuls", "effect": "Das Ziel wird mit einer Schockwelle angegriffen, die aus dem offenen Maul des Anwenders kommt."}, "dragonRush": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Anwender führt einen gefährlichen Angriff aus. Das Ziel schreckt eventuell zurück."}, "powerGem": {"name": "Juwelenkraft", "effect": "<PERSON><PERSON><PERSON> attackiert mit einem Lichtstrahl, der funk<PERSON>, als sei er aus Juwelen."}, "drainPunch": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Entzieht dem Ziel Energie. Die Hälfte des Schadens wird den KP des Anwenders zugerechnet."}, "vacuumWave": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Ein Faustwirbel sendet eine Vakuumwelle auf das Ziel. Erstschlaggarantie."}, "focusBlast": {"name": "F<PERSON>ss<PERSON><PERSON>", "effect": "Anwender erhöht seinen mentalen Fokus und greift dann an. Senkt eventuell Spezial-Verteidigung des Zieles."}, "energyBall": {"name": "Energieball", "effect": "<PERSON><PERSON><PERSON> zieht Kraft aus der Natur und feuert sie auf das Ziel. Senkt eventuell Spezial-Verteidigung des Zieles."}, "braveBird": {"name": "Sturzflug", "effect": "Anwender greift aus niedriger Höhe an. Er erleidet bei dieser Attacke selbst großen Schaden."}, "earthPower": {"name": "Erdkräfte", "effect": "Der Boden unter dem Ziel erzittert durch die Kraft der Erde. Senkt eventuell Spezial-Verteidigung."}, "switcheroo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Item wird in Windeseile mit dem Ziel getauscht."}, "gigaImpact": {"name": "Gigastoß", "effect": "Anwender rennt mit seiner ganzen Kraft gegen das Ziel an und muss dann eine Runde ruhen."}, "nastyPlot": {"name": "Ränkeschmied", "effect": "<PERSON><PERSON><PERSON>ert sein <PERSON> und hat finstere Gedanken. Steigert Spezial-<PERSON><PERSON> stark."}, "bulletPunch": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Das Ziel wird von ultraschnellen Hieben getroffen. Erstschlaggarantie."}, "avalanche": {"name": "<PERSON><PERSON>", "effect": "<PERSON><PERSON> der Anwender in dieser Runde vom Ziel getroffen, macht diese Attacke doppelten Schaden."}, "iceShard": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Das Ziel wird mit Eisklumpen beworfen. Diese Attacke hat Erstschlaggarantie."}, "shadowClaw": {"name": "Dunkelklaue", "effect": "Das Ziel wird mit scharfen Klauen aus der Schattenwelt attackiert. Hohe Volltrefferquote."}, "thunderFang": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON>der beißt mit elektrifizierten Reißzähnen zu. Das Ziel schreckt eventuell zurück oder wird paralysiert."}, "iceFang": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON>der beißt mit eiskalten Reißzähnen zu. <PERSON><PERSON> schreckt eventuell zurück oder friert ein."}, "fireFang": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON>der beißt mit flammenden Reißzähnen zu. <PERSON><PERSON> schreckt eventuell zurück oder verbrennt sich."}, "shadowSneak": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON> erwei<PERSON>t Schatten und greift das Ziel von hinten an. Erstschlaggarantie."}, "mudBomb": {"name": "Schl<PERSON>mbombe", "effect": "Anwender greift mit einem festen Schlammklumpen an. Senkt eventuell Genauigkeit des Zieles."}, "psychoCut": {"name": "Psy<PERSON>kling<PERSON>", "effect": "Das Ziel wird mit Klingen attackiert, die aus Psycho-Energie bestehen. Hohe Volltrefferquote."}, "zenHeadbutt": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Anwender konzentriert seinen Willen und rammt das Ziel. Dieses schreckt eventuell zurück."}, "mirrorShot": {"name": "Spiegelsalve", "effect": "<PERSON><PERSON><PERSON> feuert Energiestrahl aus seinem Körper ab. Senkt eventuell Genauigkeit des Zieles."}, "flashCannon": {"name": "Lichtkanone", "effect": "Anwender sammelt Lichtenergie und feuert sie auf einmal ab. Senkt eventuell Spezial-Verteidigung des Zieles."}, "rockClimb": {"name": "<PERSON><PERSON><PERSON>", "effect": "Eine stürmische Attacke, die das Ziel eventuell verwirrt."}, "defog": {"name": "Auflockern", "effect": "Starker Wind hebt Attacken wie Reflektor und Lichtschild des Zieles auf. Senkt außerdem den Fluchtwert."}, "trickRoom": {"name": "Bizarroraum", "effect": "<PERSON>wender erzeugt einen bizarren Raum, in dem langsame Pokémon fünf Runden lang zuerst agieren."}, "dracoMeteor": {"name": "Draco Meteor", "effect": "Kometen werden heraufbeschworen. Der Rückstoß reduziert den Spezial-Angriff des Anwenders stark."}, "discharge": {"name": "Ladungss<PERSON><PERSON>", "effect": "Anwender greift alle Pokémon im Umkreis mit Elektrizität an. Diese werden eventuell auch paralysiert."}, "lavaPlume": {"name": "Flammensturm", "effect": "Gre<PERSON> alles in seiner Umgebung mit tiefroten Flammen an. Ziel kann Verbrennungen erleiden."}, "leafStorm": {"name": "Blättersturm", "effect": "Anwender erzeugt einen Sturm aus scharfen Blättern. Rückstoß senkt Spezial-Angriff des Anwenders stark."}, "powerWhip": {"name": "Blattgeißel", "effect": "<PERSON><PERSON>der wirbelt seine Ranken oder Tentakel peitschenartig gegen das Ziel."}, "rockWrecker": {"name": "Felswerfer", "effect": "Anwender wirft einen riesigen Felsen auf das Ziel. In der nächsten Runde muss der Anwender ruhen."}, "crossPoison": {"name": "Giftstreich", "effect": "Ein schneidender Hieb, der das Ziel eventuell vergiftet. Hat eine hohe Volltrefferquote."}, "gunkShot": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON> schießt mit Müll auf das Ziel. Vergiftet dieses eventuell."}, "ironHead": {"name": "Eisenschädel", "effect": "<PERSON>iel wird durch stahlharten Kopf des Anwenders getroffen und schreckt eventuell zurück."}, "magnetBomb": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON>iel wird durch Haftbomben getroffen. Diese Attacke trifft immer."}, "stoneEdge": {"name": "<PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON> sticht das Ziel mit spitzen Steinen. Hohe Volltrefferquote."}, "captivate": {"name": "<PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, die den Spezial-Angriff des Zieles stark senkt, falls es dem anderen Geschlecht angehört."}, "stealthRock": {"name": "Tarnsteine", "effect": "Falle mit schwebenden Steinen. In den Kampf eingewechselte Ziele nehmen Schaden."}, "grassKnot": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON>iel wird durch Gras ins Straucheln gebracht. <PERSON> schwerer das Ziel, desto mehr Schaden."}, "chatter": {"name": "Geschwätz", "effect": "Attacke mit Schallwellen. Verwirrt das Ziel."}, "judgment": {"name": "Urteilskraft", "effect": "<PERSON><PERSON><PERSON> feuert unzählige Lichtstrahlen ab. Deren Typ hängt von der gehaltenen Tafel ab."}, "bugBite": {"name": "Käferbis<PERSON>", "effect": "<PERSON>wender beißt das Ziel. Trägt dieses eine Beere, isst der Anwender sie und erhält ihren Effekt."}, "chargeBeam": {"name": "Ladestrahl", "effect": "<PERSON>iel wird von einem Elektrostrahl getroffen. Steigert eventuell Spezial-Angriff des Anwenders."}, "woodHammer": {"name": "Holzhammer", "effect": "<PERSON><PERSON><PERSON> attackiert mit seinem robusten Körper. Er erleidet dabei auch selbst großen Schaden."}, "aquaJet": {"name": "Wasserdüse", "effect": "<PERSON><PERSON> dieser Erstschlag-Attacke stürzt sich der Anwender so schnell auf das Ziel, dass er quasi unsichtbar wird."}, "attackOrder": {"name": "Schlagbefehl", "effect": "<PERSON><PERSON><PERSON> ruft seine Untergebenen zum Angriff. Hat eine hohe Volltrefferquote."}, "defendOrder": {"name": "Blockbefehl", "effect": "Untergebene bilden einen lebenden Schild um den Anwender. Steigert Verteidigung und Spezial-Verteidigung."}, "healOrder": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Untergebene heilen den Anwender. KP des Anwenders werden um 50 % der maximalen KP aufgefüllt."}, "headSmash": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Anwender greift unter Einsatz seines Lebens mit einem Kopfstoß an und nimmt dabei selbst jede Menge Schaden."}, "doubleHit": {"name": "Doppelschlag", "effect": "Anwender trifft das Ziel mit dem Schweif oder Ähnlichem. Ziel wird doppelt getroffen."}, "roarOfTime": {"name": "Zeitenlärm", "effect": "<PERSON><PERSON>der attackiert mit einer Kraft, die selbst die Zeit verzerrt. In der nächsten Runde muss er ruhen."}, "spacialRend": {"name": "Raumschlag", "effect": "Schwer<PERSON>, raumgreifende Attacke. Hohe Volltrefferquote."}, "lunarDance": {"name": "Lunartanz", "effect": "<PERSON><PERSON><PERSON> geht K.O. Das an seine Stelle tretende Pokémon hat dafür volle KP und AP. Statusprobleme werden geheilt."}, "crushGrip": {"name": "Quetschgriff", "effect": "<PERSON><PERSON> wird mit großer Kraft getroffen. Je höher die KP des Zieles, desto stärker die Attacke."}, "magmaStorm": {"name": "Lavasturm", "effect": "Das Ziel wird in einen Feuersog gezogen, der vier bis fünf Runden aktiv ist."}, "darkVoid": {"name": "Sc<PERSON><PERSON>merort", "effect": "Das Ziel wird in eine Welt der Dunkelheit gezogen und in Schlaf versetzt."}, "seedFlare": {"name": "Schocksamen", "effect": "Anwender erzeugt eine Schockwelle. Spezial-Verteidigung des Zieles wird stark gesenkt."}, "ominousWind": {"name": "Unheilböen", "effect": "Das Ziel treffen abscheuliche Winde. Steigert eventuell alle Statuswerte des Anwenders."}, "shadowForce": {"name": "Schemenkraft", "effect": "<PERSON><PERSON><PERSON> verschwindet in Runde 1 und attackiert in Runde 2. <PERSON><PERSON><PERSON> auch, wenn sich das Ziel selbst schützt."}, "honeClaws": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON>zt seine Klauen, um sie zu schärfen. Erhöht Angriff und Genauigkeit des Anwenders."}, "wideGuard": {"name": "Rundumschutz", "effect": "<PERSON><PERSON><PERSON>tzt eine Runde lang vor Angriffen, die alle Pokémon auf deiner Se<PERSON> treffen."}, "guardSplit": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Durch Psycho-Kräfte werden Verteidigung und Spezial-Verteidigung des Anwenders und des Zieles addiert und in zwei gleiche Hälften geteilt."}, "powerSplit": {"name": "Kraftteiler", "effect": "Durch Psycho-Kräfte werden Angriff und Spezial-Angriff des Anwenders und des Zieles addiert und in zwei gleiche Hälften geteilt."}, "wonderRoom": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Anwender erzeugt bizarren Raum, in dem über fünf Runden die Verteidigung aller Pokémon mit ihrer Spezial-Verteidigung getauscht wird."}, "psyshock": {"name": "Psychoschock", "effect": "<PERSON><PERSON>der erzeugt eine seltsame Energiewelle, die dem Ziel physischen Schaden zufügt."}, "venoshock": {"name": "Giftschock", "effect": "Überschüttet das Ziel mit einer speziellen toxischen Flüssigkeit. Do<PERSON>t so stark gegen vergiftete Ziele."}, "autotomize": {"name": "Autotomie", "effect": "Anwender trennt sich von überflüssigen Körperteilen und steigert seine Initiative stark. Sein Gewicht nimmt deutlich ab."}, "ragePowder": {"name": "Wutpulver", "effect": "Anwender zieht gegnerische Aufmerksamkeit und Angriffe auf sich, indem er ein Wut erzeugendes Pulver über sich streut."}, "telekinesis": {"name": "Telekinese", "effect": "Bringt das Ziel durch Psycho-Kräfte zum Schweben. Dieses lässt sich so über drei Runden hinweg besonders leicht treffen."}, "magicRoom": {"name": "Magieraum", "effect": "Anwender erzeugt einen bizarren Raum, in dem über fünf Runden die Wirkung aller von Pokémon getragenen Items aufgehoben ist."}, "smackDown": {"name": "Katapult", "effect": "Greift das Ziel mit Steinen und Wurfgeschossen an. Fliegende Ziele fallen dabei vom Himmel und landen auf dem Boden."}, "stormThrow": {"name": "Bergsturm", "effect": "Ein Angriff mit voller Wucht und Volltreffergarantie."}, "flameBurst": {"name": "Funkenflug", "effect": "Bei Erfolg greift der Anwender mit berstenden Feuerblasen an. Die Funken der geplatzten Blasen treffen auch benachbarte Ziele."}, "sludgeWave": {"name": "Schlammwoge", "effect": "Greift Pokémon in der Nähe des Anwenders mit einer Schlammwelle an. Diese werden eventuell vergiftet."}, "quiverDance": {"name": "Falterreigen", "effect": "Anwender legt behände einen mystischen, formvollendeten Tanz aufs Parkett. Spezial-Angriff, Spezial-Verteidigung und Initiative steigen."}, "heavySlam": {"name": "Rammboss", "effect": "<PERSON><PERSON>der rammt das Ziel mit massivem Körper. Je schwerer er im Vergleich zum Ziel ist, desto stärker die Attacke."}, "synchronoise": {"name": "Synchrolärm", "effect": "<PERSON>ügt <PERSON> vom selben Typ, die sich in der Nähe des Anwenders aufhalten, mit seltsamen Druckwellen Schaden zu."}, "electroBall": {"name": "Elektroball", "effect": "Je höher die Initiative des Anwenders im Vergleich zum Ziel ist, desto stärker trifft dieses eine geballte Ladung Strom."}, "soak": {"name": "Überflutung", "effect": "Überschüttet das Ziel mit Unmengen an Wasser und ändert den Typ damit in Wasser um."}, "flameCharge": {"name": "Nitroladung", "effect": "<PERSON><PERSON>der hüllt sich in Flammen und greift das Ziel an. Sammelt seine Energie und erhöht dadurch die eigene Initiative."}, "coil": {"name": "Einrollen", "effect": "Anwender rollt sich zusammen und sammelt sich. Dabei werden Angriff, Verteidigung und Genauigkeit erhöht."}, "lowSweep": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Anwender greift mit blitzschnellen Bewegungen die Beine des Zieles an und senkt dessen Initiative."}, "acidSpray": {"name": "Säurespeier", "effect": "Anwender greift an, indem er eine ätzende Flüssigkeit auf das Ziel speit. Senkt dessen Spezial-Verteidigung stark."}, "foulPlay": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON> macht sich die Kraft des Zieles zunutze. <PERSON>öher des<PERSON> Angriff, desto mehr Sc<PERSON>en richtet die Attacke an."}, "simpleBeam": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Bestrahlt das Ziel mit mysteriösen Energiewellen. Bei einem Treffer wird dessen Fähigkeit zu Wankelmut."}, "entrainment": {"name": "<PERSON>wang<PERSON>", "effect": "Anwender tanzt zu einem seltsamem Rhythmus und zwingt das Ziel mitzumachen. Dieses nimmt dabei die Fähigkeit des Anwenders an."}, "afterYou": {"name": "Galanterie", "effect": "Anwender ermöglicht dem Ziel direkt nach ihm zu handeln, solange der Anwender als Erstes zum Zug kommt."}, "round": {"name": "<PERSON><PERSON>", "effect": "Angriff mit Gesang. Singt der Anwender mit allen im Kanon, steigt die Stärke."}, "echoedVoice": {"name": "Widerhall", "effect": "Angriff mit widerhallender Stimme. Wenn in jeder Runde ein Teilnehmer wiederholt die Attacke einsetzt, steigt die Stärke."}, "chipAway": {"name": "Zermürben", "effect": "Eine durchdachte Attacke zu rechter Zeit. Richtet unabhängig von den Statusveränderungen des Zieles Schaden an."}, "clearSmog": {"name": "Klärsmog", "effect": "Anwender greift das Ziel mit spezialgefertigten Schlammklumpen an. Setzt Statusveränderungen zurück."}, "storedPower": {"name": "Kraftvorrat", "effect": "Angriff mit angesparter Energie. Je höher die Statuswerte des Anwenders, desto stärker fällt die Attacke aus."}, "quickGuard": {"name": "Rapidschutz", "effect": "Schützt Anwender und Mitstreiter vor gegnerischen Erstschlag-Attacken."}, "allySwitch": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Wundersame Kräfte teleportieren den Anwender an den Platz eines Mitstreiters."}, "scald": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Heizt dem Ziel mit einem Schwall siedend heißen Kochwassers ein. Das Ziel erleidet dabei eventuell Verbrennungen."}, "shellSmash": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Anwender zerbricht seine Schale und senkt seine Verteidigung und Spezial-Verteidigung, aber dafür steigen Angriff, Spezial-Angriff und Initiative stark."}, "healPulse": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON> löst eine Schmerzen lindernde Welle aus und heilt dabei das Ziel mit der Hälfte seiner maximalen KP."}, "hex": {"name": "<PERSON><PERSON><PERSON>", "effect": "Eine Attacke, bei der der Anwender das Ziel bedrängt. <PERSON><PERSON><PERSON> mit Statusproblemen hohen Schaden zu."}, "skyDrop": {"name": "<PERSON><PERSON><PERSON>", "effect": "Steigt in Runde 1 mit dem Ziel in die Luft auf und lässt es in Runde 2 fallen. Das Ziel kann dabei nicht angreifen."}, "shiftGear": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Durch Drehen der Zahnräder erhöht sich nicht nur der Angriffs-Wert, sondern auch die Initiative des Anwenders stark."}, "circleThrow": {"name": "Überkopfwurf", "effect": "Schleudert das Ziel davon und bewirkt damit, dass ein anderes Pokémon eingewechselt wird. Beendet Kämpfe gegen wilde Pokémon."}, "incinerate": {"name": "Ein<PERSON><PERSON><PERSON>", "effect": "Eine Feuer-Attacke. Trägt das Ziel e<PERSON> Beere oder ein ähnliches Item bei sich, wird dieses von den Flammen verzehrt und geht verloren."}, "quash": {"name": "Verzögerung", "effect": "Anwender stemmt sich gegen das Ziel und bewirkt, dass dieses erst als Letztes angreift."}, "acrobatics": {"name": "Akrobatik", "effect": "Ein graziler Angriff auf das Ziel. Trägt der Anwender kein Item bei sich, richtet die Attacke großen Schaden an."}, "reflectType": {"name": "Typenspiegel", "effect": "Anwender bildet das Ziel nach und nimmt dabei dessen Typ an."}, "retaliate": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON>der nimmt Rache für einen besiegten Mitstreiter. Wu<PERSON> in der vorigen Runde ein Mitstreiter besiegt, steigt die Kraft."}, "finalGambit": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Ein Angriff, der dem Ziel Schaden in Höhe der aktuellen KP des Anwenders zufügt. Letzterer wird dadurch selbst besiegt."}, "bestow": {"name": "Offerte", "effect": "Trägt das Ziel gerade kein Item bei sich, erhält es das Item, das sich aktuell im Besitz des Anwenders befindet."}, "inferno": {"name": "Inferno", "effect": "Anwender greift das Ziel an, indem er es mit dichten Flammen umhüllt. Ziel erleidet Verbrennungen."}, "waterPledge": {"name": "Wassersäulen", "effect": "Ein Angriff mit Wassersäulen. Mit Feuersäulen kombiniert steigt die Wirkung und ein Regenbogen erscheint."}, "firePledge": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Ein Angriff mit Feuersäulen. Mit Pflanzsäulen kombiniert steigt die Wirkung und die Umgebung wird zu einem Meer aus Feuer."}, "grassPledge": {"name": "Pflanzensäulen", "effect": "Ein Angriff mit Pflanzsäulen. Mit Wassersäulen kombiniert steigt die Wirkung und die Umgebung wird zu einem Sumpf."}, "voltSwitch": {"name": "Voltwechsel", "effect": "An<PERSON>der kehrt nach dem Angriff mit atemberaubender Geschwindigkeit zurück und tauscht Platz mit einem anderen Pokémon."}, "struggleBug": {"name": "Käfertrutz", "effect": "Anwender leistet Widerstand und greift an. Der Spezial-Angriff der Ziele sinkt."}, "bulldoze": {"name": "Dampf<PERSON><PERSON>", "effect": "Anwender greift an, indem er den Boden um sich herum plattwalzt. Die Initiative aller betroffenen Pokémon sinkt."}, "frostBreath": {"name": "Eisesodem", "effect": "Anwender greift an, indem er dem Ziel eisigen Atem entgegenhaucht. Volltreffergarantie."}, "dragonTail": {"name": "Drachenrute", "effect": "Putzt das Ziel vom Feld und wechselt es mit einem anderen Pokémon aus. Beendet Kämpfe gegen wilde Pokémon."}, "workUp": {"name": "Kraftschub", "effect": "Anwender erhält einen Kraftschub, der seinen Angriff und Spezial-Angriff erhöht."}, "electroweb": {"name": "Elektronetz", "effect": "Fängt Ziele mit einem elektrischen Netz und senkt deren Initiative."}, "wildCharge": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Anwender erzeugt Spannung und greift an, indem er auf Kollisionskurs geht. Er selbst erleidet dabei ebenfalls leichten Schaden."}, "drillRun": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON> rammt das Ziel, während er seinen Körper wie einen Bohrer dreht. Hohe Volltrefferquote."}, "dualChop": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Versetzt dem Ziel mit massiven Extremitäten Hiebe. Angriff erfolgt zweimal hintereinander."}, "heartStamp": {"name": "Herzstempel", "effect": "Verleitet Ziel durch Kokettieren zu Unachtsamkeit und verpasst ihm dann einen harten Schlag. Z<PERSON> schreckt eventuell zurück."}, "hornLeech": {"name": "Holzgeweih", "effect": "Greift Ziel mit Astgeweih an und zapft diesem Nährstoffe ab. Anwender wird um die Hälfte des zugefügten Schadens geheilt."}, "sacredSword": {"name": "Sanctoklinge", "effect": "Schneideangriff mit langem Horn. Richtet Schaden unabhängig von Statusveränderungen des Zieles an."}, "razorShell": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Schneideangriff mit einer scharfen Muschelschale. Senkt eventuell die Verteidigung des Zieles."}, "heatCrash": {"name": "Brandstempel", "effect": "Remp<PERSON><PERSON><PERSON> mit brennendem Körper. Je schwerer der Anwender im Vergleich zum Ziel ist, desto stärker die Attacke."}, "leafTornado": {"name": "Grasmixer", "effect": "Anwender greift an, indem er das Ziel in scharfes Blattwerk einwickelt. Kann die Genauigkeit senken."}, "steamroller": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Anwender rollt mit rundlichem Körper über das Ziel und drückt es platt. Z<PERSON> schreckt eventuell zurück."}, "cottonGuard": {"name": "<PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON>der schützt sich, indem er sich in einen luftigen Flaum hüllt. Erh<PERSON>ht die Verteidigung drastisch."}, "nightDaze": {"name": "Nachtflut", "effect": "Anwender greift Ziel mit finsteren Schockwellen an. Senkt eventuell die Genauigkeit."}, "psystrike": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON>der erzeugt seltsame Energiewellen, die dem Ziel physischen Schaden zufügen."}, "tailSlap": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Anwender greift das Ziel mit seiner schlagfesten Rute zwei- bis fünfmal hintereinander an."}, "hurricane": {"name": "Orkan", "effect": "<PERSON>wender greift das Ziel an, indem er es mit heftigen Windböen umgibt. Ziel wird eventuell verwirrt."}, "headCharge": {"name": "Steinschädel", "effect": "Remp<PERSON><PERSON>cke mit ausgeflippter Retrofrisur. Anwender nimmt selbst leichten Schaden."}, "gearGrind": {"name": "Klikkdiskus", "effect": "Anwender greift an, indem er stählerne Zahnräder auf das Ziel schleudert. Angriff erfolgt zweimal hintereinander."}, "searingShot": {"name": "Flammenball", "effect": "Gre<PERSON> alles in seiner Umgebung mit tiefroten Flammen an. Ziel kann Verbrennungen erleiden."}, "technoBlast": {"name": "Techblaster", "effect": "<PERSON><PERSON>der feuert ein Lichtgeschoss auf das Ziel ab. Der Typ der Attacke hängt von dem des Moduls ab."}, "relicSong": {"name": "Urgesang", "effect": "Anwender greift mit Urgesang an, der Ziele in der Nähe im tiefsten Inneren anspricht. Diese schlafen eventuell ein."}, "secretSword": {"name": "Mystoschwert", "effect": "Schneideangriff mit dem langen Schwert des Anwenders. Die mysteriöse Kraft aus dem Horn erzeugt physischen Schaden."}, "glaciate": {"name": "Eiszeit", "effect": "Anwender greift an, indem er dem Ziel klirrend kalte Luft entgegenbläst. Senkt die Initiative des Zieles."}, "boltStrike": {"name": "Blitzschlag", "effect": "<PERSON><PERSON><PERSON> seinen Körper mit einer gewaltigen Menge an Elektrizität auf und rammt damit das Ziel. Ziel wird eventuell paralysiert."}, "blueFlare": {"name": "Blauflammen", "effect": "<PERSON><PERSON>der greift an, indem er das Ziel in wunderschöne, intensivblaue Flammen hüllt, die es eventuell verbrennen."}, "fieryDance": {"name": "Feuerreigen", "effect": "<PERSON>ü<PERSON><PERSON> das Ziel mit einer Feuerhose in Flammen. Kann den Spezial-Angriff des Anwenders erhöhen."}, "freezeShock": {"name": "Frostvolt", "effect": "Feuert in der zweiten Runde elektrisch geladene Eisklumpen auf das Ziel ab. Paralysiert das Ziel eventuell."}, "iceBurn": {"name": "<PERSON><PERSON><PERSON>", "effect": "Umgibt das Ziel in der nächsten Runde mit heftigen, alles gefrierenden Eisböen. Fügt dem Ziel eventuell Verbrennungen zu."}, "snarl": {"name": "<PERSON><PERSON><PERSON>", "effect": "<PERSON>äscht Zielen in der Nähe mit einer ausführlichen Standpauke den Kopf und senkt dabei deren Spezial-Angriff."}, "icicleCrash": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Lässt große, schwere Eiszapfen auf das Ziel herabregnen. <PERSON><PERSON> schreckt eventuell zurück."}, "vCreate": {"name": "V-Generator", "effect": "Eine Verzweiflungsattacke. Anwender entfacht glühend heißes Feuer. Senkt dessen Verteidigung, Spezial-Verteidigung und Initiative."}, "fusionFlare": {"name": "K<PERSON>uzflamme", "effect": "Feuert eine monströse Flamme ab. Wird die Attacke durch einen gigantischen Blitz modifiziert, steigt die Stärke."}, "fusionBolt": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON>uert einen monströsen Blitz ab. Wird die Attacke durch eine gigantische Flamme modifiziert, steigt die Stärke."}, "flyingPress": {"name": "Flying Press", "effect": "Der Anwender stürzt sich aus der Luft auf das Ziel. Die Attacke gehört sowohl dem Typ Kampf als auch dem Typ Flug an."}, "matBlock": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender richtet eine Tatami-Matte auf, um sich und sein Team vor Schaden zu schützen. <PERSON><PERSON> vor Status-Attacken."}, "belch": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender fügt dem Ziel Schaden zu, indem er es anrülpst. Diese Attacke gelingt nur nach dem Konsum einer getragenen Beere."}, "rototiller": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender pflügt den Boden und macht die Erde fruchtbarer. <PERSON>rhöht den Angriff und den Spezial-Ang<PERSON> von <PERSON>-Pokémon."}, "stickyWeb": {"name": "Klebenetz", "effect": "Der Anwender spinnt in der Umgebung des gegnerischen Teams ein klebriges Netz und senkt so die Initiative neu eingewechselter Pokémon."}, "fellStinger": {"name": "Stachelfinale", "effect": "<PERSON><PERSON><PERSON> es dem Anwender, das Ziel mit dieser Attacke zu besiegen, steigt sein Angriffs-<PERSON><PERSON> stark."}, "phantomForce": {"name": "Phantomkraft", "effect": "Der Anwender verschwindet, um eine Runde lang seine Kraft zu sammeln und in der nächsten Runde anzugreifen. Durchbricht die Defensive des Zieles."}, "trickOrTreat": {"name": "Halloween", "effect": "Der Anwender lehrt das Ziel das Fürchten. Dieses nimmt dadurch zusätzlich den Typ Geist an."}, "nobleRoar": {"name": "Kampfgebrüll", "effect": "Der Anwender stößt ein Kampfgebrüll aus, das das Ziel einschüchtert und zugleich seinen Angriffs- und Spezial-Angriffs-<PERSON><PERSON> senkt."}, "ionDeluge": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Versprüht elektrisch geladene Partikel und bewirkt, dass Normal-Attacken den Typ Elektro annehmen."}, "parabolicCharge": {"name": "Parabolladung", "effect": "<PERSON><PERSON><PERSON> allen <PERSON> in der Umgebung Schaden zu. Der Anwender wird um die Hälfte des insgesamt angerichteten Schadens geheilt."}, "forestsCurse": {"name": "Waldesfluch", "effect": "Der Anwender belegt das Ziel mit einem Waldesfluch, durch den dieses zusätzlich den Typ Pflanze annimmt."}, "petalBlizzard": {"name": "Blütenwirbel", "effect": "Der Anwender erzeugt einen turbulenten Blütenwirbel, der alle Pokémon in der Nähe erfasst und ihnen Schaden zufügt."}, "freezeDry": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Das Ziel wird stark abgekühlt und manchmal sogar eingefroren. Die Attacke ist sehr effektiv gegen Wasser-Pokémon."}, "disarmingVoice": {"name": "Säuselstimme", "effect": "Der Anwender stößt einen bezirzenden Ruf aus, mit dem er das Ziel in seinen Bann schlägt und ihm immer mentalen Schaden zufügt."}, "partingShot": {"name": "Abgangstirade", "effect": "<PERSON><PERSON><PERSON><PERSON><PERSON> das Ziel mit einer Abgangstirade ein, sodass dessen Angriffs- und Spezial-Angriffs-Wert sinken. Danach wird der Anwender ausgewechselt."}, "topsyTurvy": {"name": "Invertigo", "effect": "Invertiert alle Statusveränderungen des Zieles."}, "drainingKiss": {"name": "Diebeskuss", "effect": "Der Anwender stiehlt dem Ziel mit einem Kuss KP. Die Höhe der Heilung beträgt mehr als die Hälfte des beim Ziel angerichteten Schadens."}, "craftyShield": {"name": "Trickschutz", "effect": "Sch<PERSON>tzt sich und Mitstreiter mit einer mysteriösen Macht vor Status-Attacken. Es werden jedoch weiterhin KP-Schäden erlitten."}, "flowerShield": {"name": "Floraschutz", "effect": "<PERSON><PERSON><PERSON><PERSON><PERSON> mit einer mysteriösen Macht die Verteidigung aller am Kampf beteiligten Pflanzen-Pokémon."}, "grassyTerrain": {"name": "<PERSON><PERSON><PERSON>", "effect": "Verwandelt den Untergrund fünf Runden lang in ein Grasfeld und heilt in jeder neuen Runde alle Pokémon, die den Boden berühren."}, "mistyTerrain": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Verwandelt den Untergrund fünf Runden lang in ein Nebelfeld und schützt alle Pokémon, die den Boden berühren, vor Statusproblemen."}, "electrify": {"name": "Elektrifizierung", "effect": "Kommt die Attacke zum Einsatz, bevor das Ziel seine Attacke ausführt, nimmt diese für die Dauer dieser Runde den Typ Elektro an."}, "playRough": {"name": "K<PERSON><PERSON>", "effect": "Der Anwender knuddelt das Ziel und greift es an. Gelegentlich sinkt dabei auch dessen Angriffs-Wert."}, "fairyWind": {"name": "Feenbrise", "effect": "<PERSON><PERSON><PERSON>t eine Feenbrise aufkommen, die das Ziel erfasst und ihm Schaden zufügt."}, "moonblast": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender macht sich die Kraft des Mondes zunutze, um anzugreifen. Gelegentlich wird dabei der Spezial-Angriff des Zieles gesenkt."}, "boomburst": {"name": "Überschallknall", "effect": "Der Anwender greift alle Pokémon in der Umgebung mit einem gewaltigen Knall an."}, "fairyLock": {"name": "Feenschloss", "effect": "Der Anwender sperrt alle Pokémon ein und hindert sie damit in der nächsten Runde an der Flucht."}, "kingsShield": {"name": "Königsschild", "effect": "Der Anwender weicht dem gegnerischen Angriff aus und geht in die Defensive. Berührt ihn nun ein Pokémon, sinkt der Angriffs-Wert dieses Gegners."}, "playNice": {"name": "Kameradschaft", "effect": "Der Anwender schließt mit dem Ziel Freundschaft und nimmt ihm seine Angriffslust. Der Angriffs-Wert des Zieles sinkt."}, "confide": {"name": "Vertrauenssache", "effect": "Der Anwender vertraut dem Ziel ein Geheimnis an und stört auf diese Weise seine Konzentration. Der Spezial-Angriff des Zieles sinkt."}, "diamondStorm": {"name": "Diamantsturm", "effect": "Der Anwender beschwört einen zerstörerischen Diamantsturm herauf. Kann die Verteidigung des Anwenders erhöhen."}, "steamEruption": {"name": "Dampfschwall", "effect": "Der Anwender feuert einen siedend heißen Dampfschwall auf das Ziel ab. Dieses kann dabei Verbrennungen erleiden."}, "hyperspaceHole": {"name": "Dimensionsloch", "effect": "Der Anwender positioniert sich mithilfe eines Dimensionslochs direkt neben dem Ziel und durchbricht selbst Schutzschild und Scanner."}, "waterShuriken": {"name": "Wasser-<PERSON><PERSON><PERSON>", "effect": "Der Anwender schleudert dem Ziel Wurfsterne aus einem verdickten Sekret entgegen. Eine Serien-Attacke, die zwei- bis fünfmal trifft."}, "mysticalFire": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender greift das Ziel an, indem er ihm eine besondere, heiße Flamme entgegenbläst. Der Spezial-Angriff des Zieles sinkt."}, "spikyShield": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender weicht gegnerischen Angriffen aus. Gleichzeitig nehmen alle Pokémon, die mit ihm in Berührung kommen, Schaden."}, "aromaticMist": {"name": "Duftwolke", "effect": "Der Anwender erhöht mithilfe eines mysteriösen Duftes die Spezial-Verteidigung eines Mitstreiters."}, "eerieImpulse": {"name": "Mystowellen", "effect": "Der Körper des Anwenders erzeugt mysteriöse Wellen und senkt den Spezial-Angriff des Zieles dadurch stark."}, "venomDrench": {"name": "Giftfalle", "effect": "<PERSON><PERSON><PERSON> bespritzt das Ziel mit einer speziellen Giftflüssigkeit. Senkt den Angriff, den Spezial- Angriff und die Initiative von vergifteten Zielen."}, "powder": {"name": "Pulverschleuder", "effect": "Setzt das Ziel nach Einsatz von Pulverschleuder in derselben Runde eine Feuer-Attacke ein, kommt es zu einer Explosion, die ihm schadet."}, "geomancy": {"name": "Geokontrolle", "effect": "Der Anwender saugt in Runde 1 Energie auf. In Runde 2 steigen folgende Statuswerte stark: Spezial-Angriff, Spezial-Verteidigung und Initiative."}, "magneticFlux": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Das Magnetfeld wird so manipuliert, dass Spezial- Verteidigung und Verteidigung von Team-Pokémon mit der Fähigkeit Plus oder Minus steigen."}, "happyHour": {"name": "Goldene Zeiten", "effect": "Nach Einsatz der Attacke Goldene Zeiten verdoppelt sich das Preisgeld, das du im Falle eines Sieges erhältst."}, "electricTerrain": {"name": "Elektrofeld", "effect": "Verwandelt den Untergrund fünf Runden lang in ein Elektrofeld und hindert alle Pokémon, die den Boden berühren, am Einschlafen."}, "dazzlingGleam": {"name": "Zauberschein", "effect": "Der Anwender feuert einen mächtigen Lichtblitz ab, der dem Ziel Schaden zufügt."}, "celebrate": {"name": "<PERSON><PERSON><PERSON>", "effect": "Das Pokémon gratuliert dir zu deinem Geburtstag!"}, "holdHands": {"name": "Händchenhalten", "effect": "Der Anwender und ein Mitstreiter reichen einander die Hände und verfallen in einen Zustand tiefster Zufriedenheit."}, "babyDollEyes": {"name": "Kulleraugen", "effect": "Der Anwender erobert das Herz des Zieles, indem er es mit Kulleraugen ansieht. Senkt den Angriffs-Wert. Erstschlaggarantie."}, "nuzzle": {"name": "Wangenrubbler", "effect": "Der Anwender lädt seine Wangen elektrisch auf und greift an, indem er sich damit am Ziel reibt. Das Ziel wird paralysiert."}, "holdBack": {"name": "Zurückhaltung", "effect": "Der Anwender hält sich beim Angriff zurück und sorgt auf diese Wei<PERSON> da<PERSON>ür, dass dem Ziel danach mindestens 1 KP verbleibt."}, "infestation": {"name": "Plage", "effect": "Der Anwender fällt vier bis fünf Runden lang wie eine Plage über das Ziel her und greift es an. In diesem Zeitraum kann es nicht fliehen."}, "powerUpPunch": {"name": "Steigerungshieb", "effect": "Die Fäuste des Anwenders härten durch wiederholtes Zuschlagen ab. <PERSON>t jedem Treffer steigt sein Angriffs-Wert."}, "oblivionWing": {"name": "Unheilsschwingen", "effect": "Der Anwender raubt dem Ziel KP. Die Höhe der Heilung beträgt mehr als die Hälfte des beim Ziel angerichteten Schadens."}, "thousandArrows": {"name": "Tausend Pfeile", "effect": "Die Attacke erfasst auch schwebende Pokémon. Erfasst sie ein Pokémon im Schwebe-Zustand, fällt es zu Boden."}, "thousandWaves": {"name": "Tausend Wellen", "effect": "Der Anwender greift mit einer Welle an, die dicht über dem Boden verläuft und alle Pokémon, die sie erfasst, an der Flucht hindert."}, "landsWrath": {"name": "Bodengewalt", "effect": "Der Anwender sammelt die Kraft des weiten Landes und greift an, indem er sie gebündelt auf das Ziel lenkt."}, "lightOfRuin": {"name": "<PERSON><PERSON>", "effect": "Die Attacke basiert auf der Kraft des Ewigblütlers, die als mächtiger Lichtstrahl abgefeuert wird. Der Anwender nimmt dabei selbst großen Schaden."}, "originPulse": {"name": "Ursprungswoge", "effect": "Der Anwender greift das Ziel mit unzähligen blau leuchtenden Strahlen an."}, "precipiceBlades": {"name": "Abgrundsklinge", "effect": "Der Anwender wandelt die Kraft des Erdreichs in Klingen um, mit denen er das Ziel angreift."}, "dragonAscent": {"name": "Zenits<PERSON><PERSON>rmer", "effect": "Der Anwender greift das Ziel aus atemberaubender Höhe im Sturzflug an. Senkt Verteidigung und Spezial-Verteidigung des Anwenders."}, "hyperspaceFury": {"name": "Dimensionswahn", "effect": "Eine Angriffsserie mit vielen Armen, die die Wirkung von Schutzschild und Scanner durchbricht. Dabei sinkt die Verteidigung des Anwenders."}, "breakneckBlitzPhysical": {"name": "Hyper-Sprintangriff", "effect": "Der durch Z-Kraft energiegeladene Anwender rennt mit ganzer Kraft gegen das Ziel. Die Stärke variiert je nach zugrunde liegender Attacke."}, "breakneckBlitzSpecial": {"name": "Hyper-Sprintangriff", "effect": "Dummy <PERSON>"}, "allOutPummelingPhysical": {"name": "Fulminante <PERSON>", "effect": "Aus Z-Kraft hergestellte Energiebälle prallen mit voller Wucht auf das Ziel. Die Stärke variiert je nach zugrunde liegender Attacke."}, "allOutPummelingSpecial": {"name": "Fulminante <PERSON>", "effect": "Dummy <PERSON>"}, "supersonicSkystrikePhysical": {"name": "Finaler <PERSON><PERSON>", "effect": "Der Anwender schwingt sich durch Z-Kraft in die Lüfte und stürzt sich dann auf das Ziel hinab. Die Stärke variiert je nach zugrunde liegender Attacke."}, "supersonicSkystrikeSpecial": {"name": "Finaler <PERSON><PERSON>", "effect": "Dummy <PERSON>"}, "acidDownpourPhysical": {"name": "Vernichtender Säureregen", "effect": "Der Anwender kreiert mit Z-Kraft ein giftiges Moor, in dem das Ziel versinkt. Die Stärke variiert je nach zugrunde liegender Attacke."}, "acidDownpourSpecial": {"name": "Vernichtender Säureregen", "effect": "Dummy <PERSON>"}, "tectonicRagePhysical": {"name": "Seismische Eruption", "effect": "Der Anwender zerrt das Ziel mit Z-Kraft tief in den Boden und kollidiert dort mit ihm. Die Stärke variiert je nach zugrunde liegender Attacke."}, "tectonicRageSpecial": {"name": "Seismische Eruption", "effect": "Dummy <PERSON>"}, "continentalCrushPhysical": {"name": "Apokalyptische Steinpresse", "effect": "Der Anwender beschwört mit Z-Kraft einen großen Felsen herbei und lässt ihn auf das Ziel fallen. Die Stärke variiert je nach zugrunde liegender Attacke."}, "continentalCrushSpecial": {"name": "Apokalyptische Steinpresse", "effect": "Dummy <PERSON>"}, "savageSpinOutPhysical": {"name": "Wirbelnder Insektenhieb", "effect": "<PERSON><PERSON><PERSON><PERSON> von Z-Kraft umwickelt der Anwender das Ziel mit Fäden. Die Stärke variiert je nach zugrunde liegender Attacke."}, "savageSpinOutSpecial": {"name": "Wirbelnder Insektenhieb", "effect": "Dummy <PERSON>"}, "neverEndingNightmarePhysical": {"name": "<PERSON><PERSON><PERSON>", "effect": "Der Anwender beschwört mit Z-Kraft tiefen Groll herbei und lässt diesen auf das Ziel los. Die Stärke variiert je nach zugrunde liegender Attacke."}, "neverEndingNightmareSpecial": {"name": "<PERSON><PERSON><PERSON>", "effect": "Dummy <PERSON>"}, "corkscrewCrashPhysical": {"name": "Turbo-Spiralkombo", "effect": "Der Anwender wirbelt durch Z-Kraft sehr schnell umher und prallt mit dem Ziel zusammen. Die Stärke variiert je nach zugrunde liegender Attacke."}, "corkscrewCrashSpecial": {"name": "Turbo-Spiralkombo", "effect": "Dummy <PERSON>"}, "infernoOverdrivePhysical": {"name": "Dynamische Maxiflamme", "effect": "Der Anwender speit dank Z-Kraft eine gewaltige Kugel aus Flammen auf das Ziel. Die Stärke variiert je nach zugrunde liegender Attacke."}, "infernoOverdriveSpecial": {"name": "Dynamische Maxiflamme", "effect": "Dummy <PERSON>"}, "hydroVortexPhysical": {"name": "Super-Wassertornado", "effect": "Der Anwender kreiert mit Z-Kraft einen riesigen Wasserstrudel, der das Ziel verschluckt. Die Stärke variiert je nach zugrunde liegender Attacke."}, "hydroVortexSpecial": {"name": "Super-Wassertornado", "effect": "Dummy <PERSON>"}, "bloomDoomPhysical": {"name": "<PERSON><PERSON>nte Blütenpracht", "effect": "Der Anwender leiht sich durch Z-Kraft die Energie von Wiesenblumen und greift das Ziel damit an. Die Stärke variiert je nach zugrunde liegender Attacke."}, "bloomDoomSpecial": {"name": "<PERSON><PERSON>nte Blütenpracht", "effect": "Dummy <PERSON>"}, "gigavoltHavocPhysical": {"name": "Gigavolt-Funkensalve", "effect": "Der Anwender greift das Ziel mit durch Z-Kraft gesammelter starker Elektrizität an. Die Stärke variiert je nach zugrunde liegender Attacke."}, "gigavoltHavocSpecial": {"name": "Gigavolt-Funkensalve", "effect": "Dummy <PERSON>"}, "shatteredPsychePhysical": {"name": "Psycho-Schmetterschlag", "effect": "Der Anwender kontrolliert das Ziel mit Z-Kraft und macht ihm so das Leben schwer. Die Stärke variiert je nach zugrunde liegender Attacke."}, "shatteredPsycheSpecial": {"name": "Psycho-Schmetterschlag", "effect": "Dummy <PERSON>"}, "subzeroSlammerPhysical": {"name": "<PERSON><PERSON><PERSON>", "effect": "Der Anwender senkt mit Z-Kraft die Temperatur drastisch und lässt das Ziel einfrieren. Die Stärke variiert je nach zugrunde liegender Attacke."}, "subzeroSlammerSpecial": {"name": "<PERSON><PERSON><PERSON>", "effect": "Dummy <PERSON>"}, "devastatingDrakePhysical": {"name": "Drastisches Drachendröhnen", "effect": "Der Anwender materialisiert durch Z-Kraft seine Aura und greift damit das Ziel an. Die Stärke variiert je nach zugrunde liegender Attacke."}, "devastatingDrakeSpecial": {"name": "Drastisches Drachendröhnen", "effect": "Dummy <PERSON>"}, "blackHoleEclipsePhysical": {"name": "Schwarzes Loch des Grauens", "effect": "Der Anwender sammelt mit Z-Kraft dunkle Energie an, die das Ziel verschlingt. Die Stärke variiert je nach zugrunde liegender Attacke."}, "blackHoleEclipseSpecial": {"name": "Black Hole Eclipse", "effect": "Dummy <PERSON>"}, "twinkleTacklePhysical": {"name": "Entzück<PERSON>ß", "effect": "Der Anwender kreiert mit Z-Kraft eine zauberhafte Dimension und treibt dort sein Spiel mit dem Ziel. Die Stärke variiert je nach zugrunde liegender Attacke."}, "twinkleTackleSpecial": {"name": "Twinkle Tackle", "effect": "Dummy <PERSON>"}, "catastropika": {"name": "Perfektes Pika-Projektil", "effect": "Der Anwender umhüllt sich durch Z-Kraft mit gewaltiger elektrischer Energie und stürzt sich mit voller Kraft auf das Ziel."}, "shoreUp": {"name": "<PERSON><PERSON><PERSON>", "effect": "KP des Anwenders werden um 50 % der maximalen KP aufgefüllt. Tobt ein Sandsturm, werden noch mehr KP aufgefüllt."}, "firstImpression": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Eine sehr starke Attacke, die jedoch nur erfolgreich ist, wenn sie sofort eingesetzt wird, nachdem der Anwender das Kampffeld betreten hat."}, "banefulBunker": {"name": "Bunker", "effect": "Der Anwender wird vor Angriffen geschützt. Gleichzeitig werden alle Pokémon, die mit ihm in Berührung kommen, vergiftet."}, "spiritShackle": {"name": "Sc<PERSON>tenfessel", "effect": "Der Anwender greift das Ziel an und näht zugleich dessen Schatten am Boden fest, sodass es nicht entkommen kann."}, "darkestLariat": {"name": "<PERSON>", "effect": "Der Anwender wirbelt mit beiden Armen und prallt so auf das Ziel. Richtet unabhängig von den Statusveränderungen des Zieles Schaden an."}, "sparklingAria": {"name": "Schaumserenade", "effect": "Durch Gesang erzeugte Blasen werden auf das Ziel geschleudert. Alle Pokémon, die dadurch Schaden erleiden, werden auch von Verbrennungen geheilt."}, "iceHammer": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Anwender trifft mit einem starken Hieb. Senkt Initiative des Anwenders."}, "floralHealing": {"name": "Flora<PERSON>r", "effect": "KP des Zieles werden um 50 % der maximalen KP aufgefüllt. Die Wirkung steigt, wenn der Untergrund in ein Grasfeld verwandelt wurde."}, "highHorsepower": {"name": "Pferdestärke", "effect": "Der Anwender greift das Ziel mit einer heftigen Ganzkörper-Attacke an."}, "strengthSap": {"name": "Kraftabsorber", "effect": "Ein Angriff, der die KP des Anwenders um die Höhe des Angriffs-Werts des Zieles heilt. Anschließend wird der Angriff des Zieles gesenkt."}, "solarBlade": {"name": "<PERSON><PERSON><PERSON>", "effect": "Der Anwender absorbiert in der 1. Runde Licht, das er in der 2. Runde zu einem Schwert formt, mit dem er angreift."}, "leafage": {"name": "Blattwerk", "effect": "Der Anwender greift das Ziel mit Blättern an."}, "spotlight": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender lenkt die Aufmerksamkeit auf das Ziel, sodass in dieser Runde nur noch dieses Pokémon angegriffen wird."}, "toxicThread": {"name": "Giftfaden", "effect": "Der Anwender schießt giftige Fäden auf das Ziel, das dadurch vergiftet wird. Außerdem sinkt seine Initiative."}, "laserFocus": {"name": "Konzentration", "effect": "Der Anwender konzentriert sich, wodurch sein nächster Angriff garantiert ein Volltreffer wird."}, "gearUp": {"name": "<PERSON>lfsm<PERSON><PERSON><PERSON>", "effect": "Der Anwender erhöht mithil<PERSON> von Zahnrädern Angriff und Spezial-Angriff von Team-Pokémon mit der Fähigkeit Plus oder Minus."}, "throatChop": {"name": "Neck Strike", "effect": "Das <PERSON>, das von dieser Attacke getroffen wird, erleidet starke Schm<PERSON>zen und kann deswegen zwei Runden lang keine Lärm-Attacken mehr einsetzen."}, "pollenPuff": {"name": "Pollenknödel", "effect": "Der Anwender greift mit einem Ball aus Pollen an, der beim Ziel explodiert. Mitstreiter werden von einem Ball getroffen, der ihre KP auffüllt."}, "anchorShot": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender greift das Ziel an, indem er es mit einer Ankerkette umwickelt. Dadurch wird das Ziel an der Flucht gehindert."}, "psychicTerrain": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Verhindert für fünf Runden, dass Pokémon am Boden von Attacken mit hoher Erstschlagquote getroffen werden. E<PERSON><PERSON><PERSON><PERSON> die Stärke von Psycho-Attacken."}, "lunge": {"name": "Anfallen", "effect": "Der Anwender greift das Ziel mit ganzer Kraft an, wodurch auch der Angriffs-Wert des Zieles sinkt."}, "fireLash": {"name": "Feuerpeitsche", "effect": "Der Anwender greift das Ziel mit einer brennenden Peitsche an und senkt dabei zusätzlich dessen Verteidigungs-Wert."}, "powerTrip": {"name": "Überheblichkeit", "effect": "Der Anwender prahlt mit seiner Stärke und greift das Ziel an. Dieser Angriff ist umso stärker, je weiter die Statuswerte des Anwenders erhöht sind."}, "burnUp": {"name": "Ausbrennen", "effect": "Der Anwender nutzt das gesamte Feuer in seinem Körper, um großen Schaden auszuteilen. Die restliche Kampfdauer gehört er nicht mehr dem <PERSON>p Feuer an."}, "speedSwap": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender tauscht seinen Initiative-Wert mit dem des Zieles."}, "smartStrike": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender durchbohrt das Ziel mit seinem spitzen Horn. Diese Attacke trifft immer."}, "purify": {"name": "Läuterung", "effect": "Der Anwender heilt das Statusproblem des Zieles und füllt dadurch seine eigenen KP auf."}, "revelationDance": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender tanzt und greift dabei das Ziel mit voller Kraft an. Die Attacke hat denselben Typ wie das Pokémon, das sie einsetzt."}, "coreEnforcer": {"name": "Sanktionskern", "effect": "Hat das Pokémon, das durch diese Attacke Schaden genommen hat, in dieser Runde bereits gehandelt, verliert es seine Fähigkeit."}, "tropKick": {"name": "Tropenkick", "effect": "Der Anwender greift den Gegner mit einem heftigen Tritt tropischer Herkunft an. Dabei sinkt auch der Angriffs-Wert des Gegners."}, "instruct": {"name": "Kommando", "effect": "Der Anwender befiehlt dem Ziel, dessen zuletzt ausgeführte Attacke sofort wieder einzusetzen."}, "beakBlast": {"name": "Schnabelkanone", "effect": "Der Anwender erhitzt zu<PERSON>t seinen Schnabel und greift dann an. <PERSON>, die ihn während des Erhitzens berühren, erleiden Verbrennungen."}, "clangingScales": {"name": "Sc<PERSON><PERSON>nrasseln", "effect": "Der Anwender erzeugt durch das Rasseln mit seinen Schuppen ein lautes Geräusch und greift an. Anschließend sinkt seine Verteidigung."}, "dragonHammer": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender nutzt seinen Körper wie einen Hammer und stürzt sich auf das Ziel, wodurch dieses Schaden erle<PERSON>t."}, "brutalSwing": {"name": "Wir<PERSON>", "effect": "Der Anwender dreht schwungvoll seinen Körper und fügt den <PERSON> in seiner Nähe dabei Schaden zu."}, "auroraVeil": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Diese Attacke schwächt fünf Runden lang den durch physische sowie durch Spezial-Attacken erhaltenen Schaden. Kann nur bei Hagel eingesetzt werden."}, "sinisterArrowRaid": {"name": "Schatten-Pfeilregen", "effect": "Silvarro stellt mit Z-Kraft unzählige Pfeile her und lässt diese auf das Ziel niederprasseln."}, "maliciousMoonsault": {"name": "Hyper <PERSON> Crusher", "effect": "Mit seinem durch Z-Kraft gestählten Körper stürzt sich Fuegro mit ganzer Kraft auf das Ziel."}, "oceanicOperetta": {"name": "Grandiose Meeressymphonie", "effect": "Primarene ruft mit Z-Kraft große Mengen an Wasser herbei und greift damit das Ziel an."}, "guardianOfAlola": {"name": "<PERSON><PERSON><PERSON>", "effect": "Ein gewaltiger Angriff des Schutzpatrons, der durch Z-Kraft die Kraft Alolas erlangt hat. Reduziert die verbleibenden KP des Zieles stark."}, "soulStealing7StarStrike": {"name": "Sternbild des Seelenraubes", "effect": "Marshadow schlägt mit durch Z-Kraft gestärkten Schlägen und Tritten in einer Serien-Attacke auf das Ziel ein."}, "stokedSparksurfer": {"name": "Blitz-<PERSON><PERSON><PERSON><PERSON>", "effect": "Das Alola-Raichu greift das Ziel mithilfe von Z-Kraft mit voller Wucht an und paralysiert es."}, "pulverizingPancake": {"name": "<PERSON><PERSON><PERSON> mit lustig", "effect": "Relaxo wird von Z-Kraft erfüllt und macht Ernst. Es bringt seinen riesigen Körper in Schwung und stürzt sich mit ganzer Kraft auf das Ziel."}, "extremeEvoboost": {"name": "<PERSON><PERSON> der Neun", "effect": "Der Anwender macht sich durch Z-Kraft die Stärke seiner Weiterentwicklungen zunutze und erhöht seine Statuswer<PERSON> stark."}, "genesisSupernova": {"name": "Supernova des Ursprungs", "effect": "<PERSON>w greift das Ziel mithilfe von Z-Kraft mit voller Wucht an. Der Untergrund wird dabei in ein Psychofeld verwandelt."}, "shellTrap": {"name": "Panzerfalle", "effect": "Der Anwender legt eine Panzerfalle. Wird er von einer physischen Attacke getroffen, explodiert die Falle und fügt dem Angreifer Schaden zu."}, "fleurCannon": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender greift das Ziel mit einem gewaltigen Strahl an. Sein eigener Spezial-<PERSON><PERSON> sinkt dadurch stark."}, "psychicFangs": {"name": "Psychobeißer", "effect": "Der Anwender beißt das Ziel mithilfe von Psycho-Kräften. Die Attacke durchbricht auch Barrieren wie Lichtschild und Reflektor."}, "stompingTantrum": {"name": "Fruststampfer", "effect": "Von Frust getrieben greift der Anwender an. Wenn seine vorige Attacke fehlgeschlagen ist, verdoppelt sich die Stärke der Attacke."}, "shadowBone": {"name": "Schattenknochen", "effect": "Der Anwender greift das Ziel mit einem Knochen an, in dem eine Seele haust. Senkt eventuell die Verteidigung des Zieles."}, "accelerock": {"name": "Turbofelsen", "effect": "Der Anwender prallt mit großer Geschwindigkeit auf das Ziel. Hohe Erstschlagquote."}, "liquidation": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender greift das Ziel mit der Kraft des Wassers an. Senkt eventuell die Verteidigung des Zieles."}, "prismaticLaser": {"name": "Prisma-Laser", "effect": "Der Anwender feuert mithil<PERSON> von Prisma-Kraft mächtige Lichtstrahlen ab. In der nächsten Runde kann er nicht handeln."}, "spectralThief": {"name": "Diebesschatten", "effect": "Der Anwender schleicht sich in den Schatten des Zieles, stiehlt dessen erhöhte Statuswerte und fügt ihm Schaden zu."}, "sunsteelStrike": {"name": "Stahlgestirn", "effect": "Der Anwender stürzt mit der Gewalt eines Meteors auf das Ziel. Die Fähigkeit des Zieles wird dabei ignoriert."}, "moongeistBeam": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender greift mit einem unheimlichen Lichtstrahl an. Diese Attacke ignoriert die Fähigkeit des Zieles."}, "tearfulLook": {"name": "Tränendrüse", "effect": "Dem Anwender stehen <PERSON> in den Augen, wodurch das Ziel seinen Kampfeswillen verliert. Angriff und Spezial-Angriff des Zieles sinken."}, "zingZap": {"name": "Elektropikser", "effect": "Der Anwender rammt das Ziel und schockt es mit starkem Strom. Das Ziel schreckt eventuell zurück."}, "naturesMadness": {"name": "Naturzorn", "effect": "Das Ziel wird vom Zorn der Natur getroffen und verliert dadurch die Hälfte seiner KP."}, "multiAttack": {"name": "Multi-Angriff", "effect": "Der Anwender sammelt eine große Menge Energie und greift das Ziel damit an. Der Typ der Attacke hängt von dem der Disc ab."}, "tenMillionVoltThunderbolt": {"name": "Tausendfacher Donnerblitz", "effect": "Das eine Kappe tragende Pikachu greift das Ziel mit einem durch Z-Kraft verstärkten Elektroschock an. Hohe Volltrefferquote."}, "mindBlown": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender greift alle Pokémon in der Umgebung an, indem er seinen Kopf explodieren lässt. Dabei verletzt er sich auch selbst."}, "plasmaFists": {"name": "Plasmafäuste", "effect": "Ein Angriff mit elektrisch geladenen Fäusten, der bewirkt, dass Normal-Attacken den Typ Elektro annehmen."}, "photonGeyser": {"name": "Photonen-Geysir", "effect": "Ein Angriff mit einer Lichtsäule. Ist der Angriff höher als der Spezial-Angriff, wird die Höhe des Schadens durch den Angriff bestimmt und umgekehrt."}, "lightThatBurnsTheSky": {"name": "Licht des Erlöschens", "effect": "Ist der Angriff höher als der Spezial-Angriff, wird die Höhe des Schadens durch den Angriff bestimmt und umgekehrt. Ignoriert die Fähigkeit des Zieles."}, "searingSunrazeSmash": {"name": "Schmetternde <PERSON>", "effect": "Solgaleo greift das Ziel mithilfe von Z-Kraft mit voller Wucht an. Ignoriert die Fähigkeit des Zieles."}, "menacingMoonrazeMaelstrom": {"name": "Geballter Mondlaser", "effect": "Lunala greift das Ziel mithilfe von Z-Kraft mit voller Wucht an. Ignoriert die Fähigkeit des Zieles."}, "letsSnuggleForever": {"name": "<PERSON><PERSON><PERSON> K<PERSON>ddelkloppe", "effect": "Mimigma greift das Ziel mithilfe von Z-Kraft mit voller Wucht und viel Liebe an."}, "splinteredStormshards": {"name": "Fataler <PERSON>gen", "effect": "Wolwerock greift das Ziel mithilfe von Z-Kraft mit voller Wucht an. Herrschen besondere Feldeffekte, werden diese zusätzlich neutralisiert."}, "clangorousSoulblaze": {"name": "<PERSON><PERSON><PERSON>", "effect": "Grandiras greift Gegner mithil<PERSON> von Z-Kraft mit voller Wucht an. Zusätzlich werden seine Statuswerte erhöht."}, "zippyZap": {"name": "Britzelturbo", "effect": "Ein stürmischer Blitz-Angriff mit garantierter Erstschlag- und Volltrefferquote.\n"}, "splishySplash": {"name": "Plätschersurfer", "effect": "Der Anwender greift das Ziel mit einer großen, elektrisch aufgeladenen Welle an. Das Ziel wird eventuell paralysiert."}, "floatyFall": {"name": "Schwebesturz", "effect": "Der Anwender schwebt nach oben und stürzt dann unvermittelt auf das Ziel herab. Das Ziel schreckt eventuell zurück."}, "pikaPapow": {"name": "Pika-<PERSON>", "effect": "Je größer das Vertrauen des Anwenders zu seinem Trainer ist, desto stärker fällt dieser Angriff aus. Diese Attacke trifft immer."}, "bouncyBubble": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender greift mit Wasserblasen an. Seine KP werden um den vom Wasser angerichteten Schadens geheilt."}, "buzzyBuzz": {"name": "Knisterladung", "effect": "Der Anwender greift das Ziel mit Elektrizität an, wodurch dieses paralysiert wird."}, "sizzlySlide": {"name": "Flackerbrand", "effect": "Der Anwender hüllt sich in Flammen und stürzt sich beherzt auf das Ziel, welches dadurch Verbrennungen erleidet."}, "glitzyGlow": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender greift das Ziel gnadenlos mit telekinetischer Energie an. Dabei wird eine geheimnisvolle Wand erzeugt, die Spezial-Attacken des Ziels abschwächt."}, "baddyBad": {"name": "Quälzone", "effect": "Der Anwender zeigt sich von seiner dunklen Seite und greift an. Dabei wird eine geheimnisvolle Wand erzeugt, die physische Attacken des Ziels abschwächt."}, "sappySeed": {"name": "Sprießbomben", "effect": "Der Anwender lässt eine riesige Ranke wachsen, von der Samen herabfallen, die dem Ziel schaden und ihm in jeder Runde KP absaugen."}, "freezyFrost": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender greift mit einem schwarzen Kristall aus gefrorenem Nebel an. Die Statusveränderungen aller am Kampf beteiligten Pokémon werden zurückgesetzt."}, "sparklySwirl": {"name": "Glitzersturm", "effect": "Der Anwender greift an, indem es das Ziel in einen nahezu erstickend wohlriechenden Wirbelwind hüllt. Das Team des Anwenders wird von Statusproblemen geheilt."}, "veeveeVolley": {"name": "Evo-Crash", "effect": "Je größer das Vertrauen des Pokémon zu seinem Trainer ist, desto stärker fällt dieser Angriff aus. Diese Attacke trifft immer."}, "doubleIronBash": {"name": "Panzerfäuste", "effect": "Der Anwender rotiert um die Schraubenmutter in seinem Brustkorb und schlägt zweimal hintereinander mit den Armen zu. Das Ziel schreckt eventuell zurück."}, "maxGuard": {"name": "Dyna-Wall", "effect": "Anwender wehrt jede Attacke ab. Scheitert eventuell bei Wiederholung."}, "dynamaxCannon": {"name": "Dynamax-Kanone", "effect": "Der Anwender schießt einen Strahl aus seinem Kern ab. Verursacht bis zu doppelt so viel Schaden, wenn das Level des Gegners höher als die Levelgrenze ist."}, "snipeShot": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Die Attacke richtet sich gegen das ausgewählte Ziel, unab<PERSON><PERSON><PERSON><PERSON> von Fähigkeiten oder Attacken, die Angriffe auf sich ziehen."}, "jawLock": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON> und Ziel können nicht ausgetauscht werden, bis einer von ihnen kampfunfähig wird. Der Effekt endet, wenn eines der Pokémon das Kampffeld verlässt."}, "stuffCheeks": {"name": "<PERSON>ens<PERSON><PERSON>", "effect": "Der Anwender frisst die Beere, die er trägt, wodurch seine Verteidigung stark erhöht wird."}, "noRetreat": {"name": "Finalformation", "effect": "Alle Statuswerte des Anwenders werden erhöht, aber dafür kann er weder ausgewechselt werden noch fliehen."}, "tarShot": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender übergießt das Ziel mit klebrigem Teer und senkt so dessen Initiative. Dadurch wird es schwach gegenüber Feuer-Attacken."}, "magicPowder": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Das Ziel wird mit magischem Puder bestreut und nimmt den Typ Psycho an."}, "dragonDarts": {"name": "Drachenpfeile", "effect": "Der Anwender greift zweimal mit Grolldra an. Bei zwei Zielen werden beide jeweils einmal angegriffen."}, "teatime": {"name": "Teatime", "effect": "Der Anwender lädt alle am Kampf beteiligten Pokémon zu einem Teekränzchen ein, wora<PERSON><PERSON> diese die Beeren essen, die sie bei sich tragen."}, "octolock": {"name": "Octoklammer", "effect": "Das Ziel wird an der Flucht gehindert und seine Verteidigung und Spezial-Verteidigung sinken jede Runde."}, "boltBeak": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender sticht mit einem elektrisch aufgeladenen Schnabel zu. Kommt er vor dem Ziel zum Zug, verdoppelt sich die Stärke der Attacke."}, "fishiousRend": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender beißt mit seinen harten Kiemen zu. Kommt er vor dem Ziel zum <PERSON>ug, verdoppelt sich die Stärke der Attacke."}, "courtChange": {"name": "Seitenwechsel", "effect": "Durch eine mysteriöse Macht werden wirksame Effekte auf Mitstreiterseite und gegnerischer Seite getauscht."}, "maxFlare": {"name": "Dyna-Brand", "effect": "<PERSON><PERSON>-<PERSON><PERSON>, die nur Dynamax-Pokémon einsetzen können. Die Sonne brennt unbarmherzig fünf Runden lang."}, "maxFlutterby": {"name": "Dyna<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON>-<PERSON><PERSON>, die nur Dynamax-Pokémon einsetzen können. Senkt den Spezial-Angriff des Zieles."}, "maxLightning": {"name": "Dyna-Gewitter", "effect": "Eine Elektro-Attacke, die nur Dynamax-Pokémon einsetzen können. <PERSON><PERSON><PERSON><PERSON> fünf Runden lang ein Elektrofeld."}, "maxStrike": {"name": "Dyna-Angriff", "effect": "Eine Normal-Attacke, die nur Dynamax-Pokémon einsetzen können. Senkt die Initiative des Zieles."}, "maxKnuckle": {"name": "Dyna-Faust", "effect": "<PERSON><PERSON>-<PERSON><PERSON>, die nur Dynamax-Pokémon einsetzen können. Erhöht den Angriff der Mitstreiterseite."}, "maxPhantasm": {"name": "Dyna-Spuk", "effect": "<PERSON><PERSON> Geister-<PERSON><PERSON>, die nur Dynamax-Pokémon einsetzen können. Senkt die Verteidigung des Zieles."}, "maxHailstorm": {"name": "Dyna-Frost", "effect": "<PERSON><PERSON>-<PERSON><PERSON>, die nur Dynamax-Pokémon einsetzen können. Lässt fünf Runden lang einen Hagelsturm toben."}, "maxOoze": {"name": "Dyna-Giftschwall", "effect": "<PERSON><PERSON> Gift-<PERSON>e, die nur Dynamax-Pokémon einsetzen können. Erhöht den Spezial-Angriff der Mitstreiterseite."}, "maxGeyser": {"name": "Dyna-Flut", "effect": "<PERSON><PERSON>-<PERSON>e, die nur Dynamax-Pokémon einsetzen können. Löst fünf Runden lang strömenden Regen aus."}, "maxAirstream": {"name": "Dyna-Düse", "effect": "<PERSON><PERSON> F<PERSON>-<PERSON><PERSON>, die nur Dynamax-Pokémon einsetzen können. Erhöht die Initiative der Mitstreiterseite."}, "maxStarfall": {"name": "Dyna-Zauber", "effect": "<PERSON><PERSON> Feen-<PERSON>e, die nur Dynamax-Pokémon einsetzen können. <PERSON><PERSON><PERSON><PERSON> fünf Runden lang ein Nebelfeld."}, "maxWyrmwind": {"name": "Dyna-Wyrm", "effect": "<PERSON><PERSON>-<PERSON><PERSON>, die nur Dynamax-Pokémon einsetzen können. Senkt den Angriff des Zieles."}, "maxMindstorm": {"name": "Dyna-Kinese", "effect": "<PERSON><PERSON> Psycho-<PERSON><PERSON>, die nur Dynamax-Pokémon einsetzen können. <PERSON><PERSON><PERSON><PERSON> fünf Runden lang ein Psychofeld."}, "maxRockfall": {"name": "Dyna-Brocken", "effect": "<PERSON><PERSON> Gesteins-<PERSON><PERSON>, die nur Dynamax-Pokémon einsetzen können. Lässt fünf Runden lang einen Sandsturm toben."}, "maxQuake": {"name": "Dyna-<PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON> Boden-<PERSON>e, die nur Dynamax-Pokémon einsetzen können. <PERSON>rhöht die Spezial-Verteidigung der Mitstreiterseite."}, "maxDarkness": {"name": "Dyna-Dunkel", "effect": "<PERSON><PERSON>-<PERSON>e, die nur Dynamax-Pokémon einsetzen können. Senkt die Spezial-Verteidigung des Zieles."}, "maxOvergrowth": {"name": "Dyna-Flora", "effect": "<PERSON><PERSON> Pflanzen-<PERSON><PERSON>, die nur Dynamax-Pokémon einsetzen können. <PERSON><PERSON><PERSON><PERSON> fünf Runden lang ein Grasfeld."}, "maxSteelspike": {"name": "Dyna-Stahlzacken", "effect": "<PERSON><PERSON>-Attacke, die nur Dynamax-Pokémon einsetzen können. <PERSON>rhöht die Verteidigung der Mitstreiterseite."}, "clangorousSoul": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender setzt eine kleine Menge an KP ein, um alle seine Statuswer<PERSON> zu erhöhen."}, "bodyPress": {"name": "Body Press", "effect": "Der Anwender greift mit seinem ganzen Körper an. Je höher seine Verteidigung ist, desto mehr Schaden richtet er an."}, "decorate": {"name": "Verzierung", "effect": "Durch Verzierungen werden der Angriff und Spezial-Angriff des Zieles stark erhöht."}, "drumBeating": {"name": "Trommelschläge", "effect": "Der Anwender kontrolliert durch Trommeln Wurzeln, die das Ziel angreifen und dessen Initiative senken."}, "snapTrap": {"name": "<PERSON><PERSON><PERSON>", "effect": "Das Ziel wird vier bis fünf Runden lang in einem Fangeisen festgehalten und angegriffen."}, "pyroBall": {"name": "Feuerball", "effect": "Der Anwender greift mit einem Ball aus Feuer an, den er durch Anzünden eines kleinen Steins erzeugt. <PERSON><PERSON><PERSON> dem Ziel eventuell Verbrennungen zu."}, "behemothBlade": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender wird zu einem riesigen Schwert und greift das Ziel an."}, "behemothBash": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender wird zu einem riesigen Schild und greift das Ziel an."}, "auraWheel": {"name": "Aura-Rad", "effect": "Mithilfe der in den Backentaschen gespeicherten Energie greift der Anwender an und erhöht seine Initiative. <PERSON><PERSON> dies von Morpeko verwendet wird hängt der Typ der Attacke von dessen Form ab."}, "breakingSwipe": {"name": "Breitseite", "effect": "Der Anwender schwingt heftig seinen robusten Schweif, um damit gegnerische Pokémon anzugreifen und ihren Angriffs-Wert zu senken."}, "branchPoke": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender attackiert das Ziel mit einem spitzen Zweig."}, "overdrive": {"name": "Overdrive", "effect": "Der Anwender haut in die Saiten seiner Gitarre oder seines Basses und erzeugt dröhnende, kraftvolle Vibrationen, die gegnerischen Pokémon schaden."}, "appleAcid": {"name": "Apfelsäure", "effect": "Der Anwender greift mit einer aus einem sauren Apfel hergestellten säurehaltigen Flüssigkeit an. Dabei wird die Spezial-Verteidigung des Zieles gesenkt."}, "gravApple": {"name": "Gravitation", "effect": "Ein Apfel fällt aus großer Höhe herab und richtet Schaden an. Dabei wird die Verteidigung des Zieles gesenkt."}, "spiritBreak": {"name": "<PERSON>lenb<PERSON><PERSON>", "effect": "Die Attacke trifft das Ziel mit so viel Wucht, dass es den Mut verliert. Dabei wird sein Spezial-<PERSON><PERSON> gesenkt."}, "strangeSteam": {"name": "Wunderdampf", "effect": "Der Anwender stößt Dampf aus, mit dem er das Ziel angreift. Dieses wird eventuell verwirrt."}, "lifeDew": {"name": "Lebenstropfen", "effect": "Wundersames Wasser heilt die KP des Anwenders und seiner am Kampf beteiligten Mitstreiter."}, "obstruct": {"name": "A<PERSON><PERSON><PERSON>", "effect": "Der Anwender wehrt jede Attacke ab. Berü<PERSON>t ihn währenddessen ein Pokémon, sinkt dessen Verteidigung stark. Scheitert eventuell bei Wiederholung."}, "falseSurrender": {"name": "Kniefalltrick", "effect": "Der Anwender tut so, als würde er sich verneigen, und sticht dann mit seinem zerzausten Fell zu. Diese Attacke trifft immer."}, "meteorAssault": {"name": "Sternensturm", "effect": "Der Anwender greift mit seiner Lauchstange an. <PERSON> der Wucht der Attacke wird ihm jedoch so schwind<PERSON>g, dass er in der nächsten Runde nicht handeln kann."}, "eternabeam": {"name": "Unendynastrahlen", "effect": "Der mächtigste Angriff, über den Endynalos in seiner ursprünglichen Form verfügt. In der nächsten Runde kann der Anwender nicht handeln."}, "steelBeam": {"name": "Stahlstrahl", "effect": "Der Anwender schießt Stahl, den er in seinem ganzen Körper angesammelt hat, in Form eines mächtigen Strahls ab. Dabei verletzt er sich auch selbst."}, "expandingForce": {"name": "Flächenmacht", "effect": "Der Anwender greift das Ziel mit Psycho-Kräften an. Wenn ein Psychofeld aktiv ist, steigt die Stärke und es wird allen gegnerischen Pokémon Schaden zugefügt."}, "steelRoller": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender greift an und zerstört dabei etwaige Felder. Ist kein Feld aktiv, schlägt die Attacke fehl."}, "scaleShot": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender greift das Ziel zwei- bis fünfmal hintereinander mit Schuppen-Geschossen an. Erhöht die eigene Initiative, aber senkt die Verteidigung."}, "meteorBeam": {"name": "Meteorstrahl", "effect": "Der Anwender sammelt in Runde 1 kosmische Kräfte und erhöht damit seinen Spezial-Angriff, bevor er in Runde 2 das Ziel angreift."}, "shellSideArm": {"name": "Muschelwaffe", "effect": "<PERSON> nachdem, was hö<PERSON> ausf<PERSON><PERSON>, richtet diese Attacke entweder physischen oder Spezial-Schaden an. Das Ziel wird eventuell vergiftet."}, "mistyExplosion": {"name": "Nebelexplosion", "effect": "Der Anwender greift alle Pokémon im Umkreis an und wird danach kampfunfähig. Die Stärke dieser Attacke steigt, wenn ein Nebelfeld aktiv ist."}, "grassyGlide": {"name": "Grasrutsche", "effect": "Der Anwender rutscht über den Boden und greift das Ziel an. Ermöglicht den Erstschlag, wenn ein Grasfeld aktiv ist."}, "risingVoltage": {"name": "<PERSON><PERSON>pannung", "effect": "Der Anwender greift mit aus dem Boden aufsteigender Elektrizität an. Die Stärke der Attacke wird verdoppelt, wenn beim G<PERSON>ner ein Elektrofeld aktiv ist."}, "terrainPulse": {"name": "Feldimpuls", "effect": "Der Anwender nutzt die Kraft des aktiven Feldes für seinen Angriff. Der Typ und die Stärke der Attacke ändern sich je nach Art des aktiven Feldes."}, "skitterSmack": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender kriecht hinter das Ziel, greift es an und senkt dabei dessen Spezial-Angriff."}, "burningJealousy": {"name": "Neidflammen", "effect": "Der Anwender greift mit der Energie seines Neids an und fügt allen gegnerischen Pokémon, deren Status<PERSON> in dieser Runde erhöht wurden, Verbrennungen zu."}, "lashOut": {"name": "Frustventil", "effect": "Der Anwender entlädt seinen Frust in einem Angriff. Die Stärke der Attacke wird verdoppelt, wenn seine Statuswerte in dieser Runde gesenkt wurden."}, "poltergeist": {"name": "Poltergeist", "effect": "Der Anwender greift das Ziel mit dessen getragenem Item an. Die Attacke schlägt fehl, wenn das Ziel kein Item trägt."}, "corrosiveGas": {"name": "Korrosionsgas", "effect": "Der Anwender greift alle Pokémon im Umkreis mit einem ätzenden Gas an. Getragene Items werden dadurch zersetzt."}, "coaching": {"name": "Coaching", "effect": "Der Anwender sorgt durch geschickte Anweisungen dafür, dass der Angriff und die Verteidigung seiner Mitstreiter steigen."}, "flipTurn": {"name": "Rollwende", "effect": "Nach der Attacke eilt der Anwender zurück und tauscht den Platz mit einem anderen Pokémon."}, "tripleAxel": {"name": "Dreifach<PERSON><PERSON>", "effect": "Tritt das Ziel ein- bis dreimal nacheinander. Die Härte der Tritte nimmt von Treffer zu Treffer zu."}, "dualWingbeat": {"name": "Do<PERSON>flügel", "effect": "Der Anwender trifft das Ziel zweimal hintereinander mit seinen Flügeln und fügt ihm so Schaden zu."}, "scorchingSands": {"name": "Brandsand", "effect": "Der Anwender greift das Ziel mit brennend heißem Sand an und fügt ihm eventuell Verbrennungen zu."}, "jungleHealing": {"name": "Dschungelheilung", "effect": "Der Anwender wird eins mit dem Dschungel und heilt bei sich und seinen am Kampf beteiligten Mitstreitern KP und hebt jegliche Statusprobleme auf."}, "wickedBlow": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender hat den Stil des Unlichts gemeistert und führt einen fokussierten, harten Sc<PERSON>ag mit Volltreffergarantie aus."}, "surgingStrikes": {"name": "<PERSON><PERSON>ffer<PERSON><PERSON>", "effect": "Der Anwender hat den Stil des Wassers gemeistert und führt mit fließenden Bewegungen drei Angriffe in Folge mit Volltreffergarantie aus."}, "thunderCage": {"name": "Blitzgefängnis", "effect": "Das Ziel wird für vier bis fünf Runden in einem elektrischen Käfig gefangen."}, "dragonEnergy": {"name": "Drachenkräfte", "effect": "Der Anwender wandelt seine Lebenskraft in Energie um und greift gegnerische Pokémon an. Je höher seine KP sind, desto mehr Schaden wird angerichtet."}, "freezingGlare": {"name": "Eisiger Blick", "effect": "Der Anwender greift das Ziel mit Psycho-Kräften an, die er aus seinen Augen abschießt. Das Ziel friert eventuell ein."}, "fieryWrath": {"name": "Brennender Zorn", "effect": "Der Anwender wandelt seinen Z<PERSON> in eine flammende Aura um und greift damit gegnerische Pokémon an. Diese schrecken eventuell zurück."}, "thunderousKick": {"name": "<PERSON><PERSON><PERSON>", "effect": "Der Anwender bringt das Ziel mit blitzschnellen Bewegungen durcheinander und tritt dann zu. Senkt die Verteidigung des Zieles."}, "glacialLance": {"name": "Blizzardlanze", "effect": "Der Anwender wirft eine in einen Blizzard gehüllte Lanze aus Eis auf gegnerische Pokémon."}, "astralBarrage": {"name": "Astralfragmente", "effect": "Der Anwender greift gegnerische Pokémon mit vielen kleinen Spukgestalten an."}, "eerieSpell": {"name": "Sc<PERSON><PERSON>rspruch", "effect": "Der Anwender greift mit gewaltigen Psycho-Kräften an. Die AP der letzten Attacke des Zieles werden um 3 Punkte gesenkt."}, "direClaw": {"name": "Unheilsklauen", "effect": "Der Anwender greift mit zerstörerischen Klauen an. Das Ziel wird eventuell vergiftet, paralysiert oder in Schlaf versetzt."}, "psyshieldBash": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender hüllt sich in Psycho-Energie und rammt das Ziel. Außerdem steigt seine Verteidigung."}, "powerShift": {"name": "Kraftwechsel", "effect": "Der Anwender tauscht seinen Angriff mit seiner Verteidigung."}, "stoneAxe": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender greift mit seinen Felsäxten an. Dadurch verstreut er schwebende Felssplitter im Umkreis des Zieles."}, "springtideStorm": {"name": "Frühlingsorkan", "effect": "Der Anwender greift gegnerische Pokémon an, indem er sie mit heftigen Windböen voller Hassliebe umgibt. Eventuell sinkt ihr Angriff."}, "mysticalPower": {"name": "Mythenkraft", "effect": "Der Anwender greift mit einer wundersamen Kraft an. Außerdem steigt sein Spezial-Angriff."}, "ragingFury": {"name": "<PERSON><PERSON>menwu<PERSON>", "effect": "Der Anwender wütet zwei bis drei Runden lang und speit heftige Flammen aus. Danach wird er verwirrt."}, "waveCrash": {"name": "Wellentackle", "effect": "Der Anwender hüllt sich in Wasser und stürzt sich mit dem ganzen Körper auf das Ziel, wobei er selbst großen Schaden erleidet."}, "chloroblast": {"name": "Chlorostrahl", "effect": "Der Anwender greift mit einer hohen Konzentration seines Chlorophylls an, wobei er selbst Schaden erleidet."}, "mountainGale": {"name": "Frostfallwind", "effect": "Der Anwender wirft gigantische Eisbrocken auf das Ziel. Dieses schreckt eventuell zurück."}, "victoryDance": {"name": "Siegestanz", "effect": "Der Anwender führt einen wilden Tanz auf, der den Sieg herbeiführen soll. Dies erhöht seinen Angriff, seine Verteidigung und seine Initiative."}, "headlongRush": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender rammt das Ziel mit dem ganzen Körper. <PERSON><PERSON><PERSON> sinken die Verteidigung und Spezial-Verteidigung des Anwenders."}, "barbBarrage": {"name": "Giftstachelregen", "effect": "Der Anwender greift mit unzähligen Giftstacheln an und vergiftet das Ziel eventuell. Do<PERSON>t so stark gegen vergiftete Ziele."}, "esperWing": {"name": "Auraschwingen", "effect": "Ein schneidender Angriff mit durch eine Aura verstärkten Schwingen, der außerdem die Initiative des Anwenders erhöht. Hohe Volltrefferquote."}, "bitterMalice": {"name": "Niedertracht", "effect": "Der Anwender greift mit eiskaltem, schaudererregendem Hass an und senkt dabei den Angriff des Zieles."}, "shelter": {"name": "Refugium", "effect": "Der Anwender macht seine Haut so hart wie Eisen und erhöht dadurch seine Verteidigung stark."}, "tripleArrows": {"name": "Drillingspfeile", "effect": "Der Anwender tritt zu und schießt dann drei Pfeile ab. Senkt eventuell die Verteidigung des Zieles oder lässt es zurückschrecken. Hohe Volltrefferquote."}, "infernalParade": {"name": "Phantomparade", "effect": "Angriff mit unzähligen Feuerkugeln, der dem Ziel eventuell Verbrennungen zufügt. <PERSON><PERSON>t so stark gegen Ziele mit Statusproblemen."}, "ceaselessEdge": {"name": "Klingenschwall", "effect": "Der Anwender greift mit einer klingengleichen Muschelschale an und verstreut Muschelsplitter, die Stacheln zu Füßen des Zieles werden."}, "bleakwindStorm": {"name": "Polarorkan", "effect": "Der Anwender greift mit starken, kalten Winden an, die Körper und Geist erzittern lassen. Senkt eventuell die Initiative gegnerischer Pokémon."}, "wildboltStorm": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender ruft ein heftiges Unwetter herbei, um mit Wind und Blitzen anzugreifen. Gegnerische Pokémon werden eventuell paralysiert."}, "sandsearStorm": {"name": "Wüstenorkan", "effect": "Der Anwender greift gegnerische Pokémon an, indem er sie mit heftigen Windböen und brennend heißem Sand umgibt. Eventuell erleiden sie Verbrennungen."}, "lunarBlessing": {"name": "<PERSON><PERSON><PERSON>", "effect": "Der Anwender richtet ein G<PERSON>et an den Mond und heilt bei sich und seinen am Kampf beteiligten Mitstreitern KP und hebt jegliche Statusprobleme auf."}, "takeHeart": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender fasst sich ein <PERSON>, befreit sich von Statusproblemen und erhöht außerdem seinen Spezial-Angriff und seine Spezial-Verteidigung."}, "gMaxWildfire": {"name": "Giga-Feuerflug", "effect": "<PERSON><PERSON>uer-Attacke, die nur Gigadynamax-<PERSON><PERSON>rak einsetzen kann. <PERSON><PERSON><PERSON> vier Runden lang Schaden zu."}, "gMaxBefuddle": {"name": "Giga-Benebelung", "effect": "<PERSON><PERSON>-<PERSON><PERSON>, die nur Gigadynamax-Smettbo einsetzen kann. Gegnerische Pokémon werden entweder vergiftet, paralysiert oder in Schlaf versetzt."}, "gMaxVoltCrash": {"name": "Giga-Blitzhagel", "effect": "<PERSON>e Elektro-Attacke, die nur Gigadynamax-Pikachu einsetzen kann. Gegnerische Pokémon werden paralysiert."}, "gMaxGoldRush": {"name": "Giga-Münzregen", "effect": "Eine Normal-Attacke, die nur Gigadynamax-Mauzi einsetzen kann. Verwirrt Gegner und bringt nach dem Kampf Geld ein."}, "gMaxChiStrike": {"name": "Giga-Fokusschlag", "effect": "<PERSON>e Kampf-Attacke, die nur Gigadynamax-Machomei einsetzen kann. Erh<PERSON>ht die Volltrefferquote auf Mitstreiterseite."}, "gMaxTerror": {"name": "Giga-Spuksperre", "effect": "<PERSON><PERSON> Geister-Attack<PERSON>, die nur Gigadynamax-<PERSON><PERSON> einsetzen kann. Hindert gegnerische Pokémon an der Flucht beziehungsweise am Auswechseln."}, "gMaxResonance": {"name": "Giga-Melodie", "effect": "<PERSON><PERSON> E<PERSON>-Attack<PERSON>, die nur Gigadynamax-<PERSON><PERSON><PERSON> einsetzen kann. Reduziert fünf Runden lang den erlittenen Schaden."}, "gMaxCuddle": {"name": "Giga-Gekuschel", "effect": "Eine Normal-Attacke, die nur Gigadynamax-Evoli einsetzen kann. Gegnerische Pokémon verlieben sich in es."}, "gMaxReplenish": {"name": "Giga-Recycling", "effect": "Eine Normal-Attacke, die nur Gigadynamax-Relaxo einsetzen kann. Stellt bereits verzehrte Beeren wieder her."}, "gMaxMalodor": {"name": "Giga-Gestank", "effect": "<PERSON><PERSON> Gift-<PERSON>e, die nur Gigadynamax-Deponitox einsetzen kann. Vergiftet gegnerische Pokémon."}, "gMaxStonesurge": {"name": "Giga-Geröll", "effect": "<PERSON><PERSON> Wasser-Attacke, die nur Gigadynamax-<PERSON><PERSON> einsetzen kann. Verstreut viele spitze Steinbrocken auf dem Kampffeld."}, "gMaxWindRage": {"name": "Giga-Sturmstoß", "effect": "<PERSON>e Flug-Attacke, die nur Gigadynamax-Krarmor einsetzen kann. Beseitigt die Effekte von Attacken wie Reflektor und Lichtschild.."}, "gMaxStunShock": {"name": "Giga-Voltschlag", "effect": "Eine Elektro-Attacke, die nur Gigadynamax-Riffex einsetzen kann. Vergiftet oder paralysiert gegnerische Pokémon."}, "gMaxFinale": {"name": "Giga-Lichtblick", "effect": "<PERSON>e Feen-Attacke, die nur Gigadynamax-Pokusan einsetzen kann. Füllt die KP auf Mitstreiterseite auf."}, "gMaxDepletion": {"name": "Giga-Dämpfer", "effect": "<PERSON><PERSON> Drachen-<PERSON><PERSON>, die nur Gigadynamax-Duraludon einsetzen kann. AP der letzten Attacke, die gegnerische Pokémon eingesetzt haben, werden gesenkt."}, "gMaxGravitas": {"name": "Giga-Astrowellen", "effect": "<PERSON>e Psycho-Attacke, die nur Gigadynamax-Maritellit einsetzen kann. Ändert die Erdanziehung für fünf Runden."}, "gMaxVolcalith": {"name": "Giga-Schlacke", "effect": "<PERSON><PERSON> Gesteins-Attacke, die nur Gigadynamax-Montecarbo einsetzen kann. <PERSON>ü<PERSON> vier Runden lang Schaden zu."}, "gMaxSandblast": {"name": "Giga-Sandstoß", "effect": "<PERSON>e Boden-<PERSON><PERSON>, die nur Gigadynamax-Sanaconda einsetzen kann. Eine Sandhose wütet für vier bis fünf Runden."}, "gMaxSnooze": {"name": "Giga-Gähnzwang", "effect": "<PERSON>e Unlicht-Attacke, die nur Gigadynamax-Olangaar einsetzen kann. Mit einem großen Gähner wird das Ziel müde gemacht und schläft in der nächsten Runde ein."}, "gMaxTartness": {"name": "Giga-Säureguss", "effect": "Eine Pflanzen-Attack<PERSON>, die nur Gigadynamax-Drapfel einsetzen kann. Senkt den Ausweichwert der gegnerischen Pokémon."}, "gMaxSweetness": {"name": "Giga-Nektarflut", "effect": "Eine Pflanzen-Attack<PERSON>, die nur Gigadynamax-Schlapfel einsetzen kann. Heilt Statusprobleme auf Mitstreiterseite."}, "gMaxSmite": {"name": "Giga-Sanktion", "effect": "<PERSON>e Feen-<PERSON><PERSON>, die nur Gigadynamax-Silembrim einsetzen kann. Verwirrt gegnerische Pokémon."}, "gMaxSteelsurge": {"name": "Giga-Stahlschlag", "effect": "<PERSON><PERSON> Stahl-Attacke, die nur Gigadynamax-Patinaraja einsetzen kann. Verstreut viele zackige Stahlsplitter auf dem Kampffeld."}, "gMaxMeltdown": {"name": "Giga-Schmelze", "effect": "Eine Stahl-Attacke, die nur Gigadynamax-Melmetal einsetzen kann. Hindert Gegner am wiederholten Einsatz derselben Attacke."}, "gMaxFoamBurst": {"name": "Giga-Schaumbad", "effect": "<PERSON><PERSON>-<PERSON><PERSON>, die nur Gigadynamax-King<PERSON> einsetzen kann. Senkt die Initiative der gegnerischen Pokémon stark."}, "gMaxCentiferno": {"name": "Giga-Feuerkessel", "effect": "<PERSON><PERSON> Feuer-Attacke, die nur Gigadynamax-Infernopod einsetzen kann. Schließt gegnerische Pokémon vier bis fünf Runden in wirbelnden Flammen ein."}, "gMaxVineLash": {"name": "Giga-Geißel", "effect": "Eine Pflanzen-Attacke, die nur Gigadynamax-Bisaflor einsetzen kann. Geißelt gegnerische Pokémon vier Runden lang mit peitschenartigen Ranken."}, "gMaxCannonade": {"name": "Giga-Beschuss", "effect": "<PERSON><PERSON> Wasser-Attacke, die nur Gigadynamax-Turtok einsetzen kann. Schließt gegnerische Pokémon vier Runden lang in einem Wasserwirbel ein."}, "gMaxDrumSolo": {"name": "Giga-Getrommel", "effect": "Eine Pflanzen-Attacke, die nur Gigadynamax-Gortrom einsetzen kann. Ignoriert die Effekte der gegnerischen Fähigkeiten."}, "gMaxFireball": {"name": "Giga-Brandball", "effect": "<PERSON><PERSON> Feuer-Attacke, die nur Gigadynamax-Liberlo einsetzen kann. Ignoriert die Effekte der gegnerischen Fähigkeiten."}, "gMaxHydrosnipe": {"name": "Giga-Schütze", "effect": "<PERSON><PERSON> Was<PERSON>-Attacke, die nur Gigadynamax-Intelleon einsetzen kann. Ignoriert die Effekte der gegnerischen Fähigkeiten."}, "gMaxOneBlow": {"name": "Giga-Einzelhieb", "effect": "<PERSON>e Unlicht-Attacke, die nur Gigadynamax-<PERSON><PERSON><PERSON><PERSON> einsetzen kann. Dieser Einzelhieb ignoriert die schützende Wirkung von Dyna-Wall."}, "gMaxRapidFlow": {"name": "Giga-Multihieb", "effect": "<PERSON><PERSON>-Attacke, die nur Gigadynamax-<PERSON><PERSON><PERSON><PERSON> einsetzen kann. Dieser Multihieb ignoriert die schützende Wirkung von Dyna-Wall."}, "teraBlast": {"name": "Tera-Ausbruch", "effect": "Ist der Anwender terakristallisiert, greift er mit Energie seines Tera-Typs an. Der Schaden hängt vom Angriff oder Spezial-<PERSON>riff ab, je nachdem, welcher Wert höher ist."}, "silkTrap": {"name": "Fadenfalle", "effect": "Der Anwender spannt eine Falle aus Fäden und wird so vor Angriffen geschützt. Berü<PERSON>t ihn nun ein Angreifer, sinkt dessen Initiative."}, "axeKick": {"name": "<PERSON><PERSON><PERSON>", "effect": "Der Anwender greift an, indem er seine erhobene Ferse hinunterschnellen lässt. Das Ziel wird eventuell verwirrt. Bei Misserfolg verletzt sich der Anwender selbst."}, "lastRespects": {"name": "Letzte Ehre", "effect": "Der Anwender rächt gefallene Mitstreiter. Je mehr kampfunfähige Pokémon sich im Team befinden, desto stärker ist die Attacke."}, "luminaCrash": {"name": "Lichteinschlag", "effect": "Der Anwender greift an, indem er ein sonderbares Licht freisetzt, das sich auch auf die Psyche auswirkt. Zudem wird die Spezial-Verteidigung des Zieles stark gesenkt."}, "orderUp": {"name": "Auftischen", "effect": "Eine Attacke mit geübten Bewegungen. Trägt der Anwender ein Nigiragi im Maul, erhöht sich je nach dessen Form ein Statuswert des Anwenders."}, "jetPunch": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON> dieser Erstschlag-Attacke hüllt der Anwender seine Faust in einen Strudel und greift mit einem extrem schnellen Hieb an."}, "spicyExtract": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender setzt eine unglaublich scharfe Essenz frei, die den Angriff des Zieles stark erhöht, aber seine Verteidigung stark senkt."}, "spinOut": {"name": "Reifendrehung", "effect": "Der Anwender wirbelt wild umher, indem er sein Gewicht auf seine Extremitäten verlagert, und richtet so Schaden an. Seine eigene Initiative sinkt dadurch stark"}, "populationBomb": {"name": "Mäuseplage", "effect": "Der Anwender versammelt eine Schar von Artgenossen, die dann geschlossen angreift und das Ziel ein- bis zehnmal hintereinander trifft."}, "iceSpinner": {"name": "Eisk<PERSON><PERSON>", "effect": "Der Anwender hüllt seine Füße in dünnes Eis, wirbelt herum und greift so das Ziel an. Die Drehung zerstört etwaige Felder"}, "glaiveRush": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender stürzt sich waghalsig auf das Ziel. Bis zum nächsten Zug des Anwenders treffen ihn gegnerische Angriffe garantiert und richten doppelten Schaden an."}, "revivalBlessing": {"name": "Vitalsegen", "effect": "Der Anwender belebt mit einem Wunsch voller Mitgefühl ein kampfunfähiges Team-Mit<PERSON><PERSON> wieder und stellt die Hälfte dessen maximaler K<PERSON> wieder her."}, "saltCure": {"name": "Pökelsalz", "effect": "Der Anwender pökelt das Ziel mit <PERSON>z e<PERSON>, wodurch dieses jede Runde Schaden erleidet. Stahl- und Wasser-Pokémon leiden besonders darunter."}, "tripleDive": {"name": "Tauchtriade", "effect": "Der Anwender taucht mit perfekt abgestimmtem Timing ab und trifft das Ziel mit Wasserspritzern. Dabei richtet er dreimal hintereinander Schaden an."}, "mortalSpin": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender greift mit einer wirbelnden Attacke an, die Gegner auch vergiftet. Befreit den Anwender unter and<PERSON><PERSON>, Klammergriff und Egelsamen."}, "doodle": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender kopiert die wahre Essenz des Zieles. Dadurch erhalten alle Pokémon auf der Mitstreiterseite die Fähigkeit des Zieles."}, "filletAway": {"name": "Abspaltung", "effect": "Der Anwender setzt seine KP ein, um seinen Angriff, seinen Spezial-Angriff und seine Initiative stark zu erhöhen."}, "kowtowCleave": {"name": "Kniefallspalter", "effect": "Der Anwender fällt auf die Knie und verleitet das Ziel zu Unachtsamkeit, bevor er mit einer Klinge zuschlägt. Diese Attacke trifft garantiert."}, "flowerTrick": {"name": "<PERSON><PERSON><PERSON>", "effect": "Der Anwender greift an, indem er dem Ziel einen Trick-Strauß zuwirft. Diese Attacke trifft immer und hat zudem Volltreffergarantie."}, "torchSong": {"name": "Loderlied", "effect": "Der Anwender spuckt inbrünstig lodernde Flammen, als würde er singen, und versengt das Ziel. Dadurch steigt auch der Spezial-Angriff des Anwenders."}, "aquaStep": {"name": "Wogentanz", "effect": "Der Anwender neckt das Ziel mit flinken, fließenden Tanzschritten und greift es dann an. <PERSON><PERSON>ch steigt auch die Initiative des Anwenders."}, "ragingBull": {"name": "Rasender Stier", "effect": "Ein rasender Angriff eines wilden Stiers, der auch Barrieren wie Lichtschild und Reflektor durchbricht. Der Attacken-Typ hängt von der Form des Anwenders ab."}, "makeItRain": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender greift an, indem er Unmengen an Münzen ausschüttet, senkt dabei aber seinen Spezial-Angriff. Das Geld wird nach dem Kampf aufgesammelt."}, "psyblade": {"name": "Psychoschneide", "effect": "Das Ziel wird mit einer immateriellen Klinge angegriffen. Die Stärke der Attacke steigt um 50 %, wenn beim Anwender ein Elektrofeld aktiv ist."}, "hydroSteam": {"name": "Hydrodampf", "effect": "Das Ziel wird kraftvoll mit brodelndem Wasser übergossen. <PERSON>r Erwarten sinkt die Stärke der Attacke bei starkem Sonnenlicht nicht, sondern steigt um 50 %."}, "ruination": {"name": "<PERSON><PERSON><PERSON>", "effect": "Der Anwender beschwört Verderben bringendes Unheil herauf und halbiert die KP des Zieles."}, "collisionCourse": {"name": "Kollisionskurs", "effect": "Der Anwender wechselt seine Form, während er sich gen Boden stürzt, und verursacht eine riesige Ur-Explosion. Ist die Attacke sehr effektiv, steigt ihre Stärke noch mehr."}, "electroDrift": {"name": "Blitztour", "effect": "Der Anwender wechselt bei rasantem Tempo seine Form und trifft das Ziel mit einem futuristischen Elektroschlag. Ist die Attacke sehr effektiv, steigt ihre Stärke noch mehr."}, "shedTail": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender setzt seine KP ein, um einen Doppelgänger zu erzeugen, und tauscht dann den Platz mit einem anderen Pokémon."}, "chillyReception": {"name": "Eisige Stimmung", "effect": "Der Anwender sorgt mit einem schlechten Witz für eisige Stimmung und tauscht den Platz mit einem anderen Pokémon. Erzeugt fünf Runden lang Schnee."}, "tidyUp": {"name": "Aufräumen", "effect": "Die Effekte von <PERSON>ler, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Giftspitzen und Delegator werden aufgehoben. Zudem steigen der Angriff und die Initiative des Anwenders."}, "snowscape": {"name": "Schneelandschaft", "effect": "<PERSON><PERSON><PERSON><PERSON> fün<PERSON>den lang Schnee. Dad<PERSON><PERSON> wird die Verteidigung von Eis-Pokémon erhöht."}, "pounce": {"name": "An<PERSON><PERSON><PERSON>", "effect": "Der Anwender greift an, indem er das Ziel anspringt. <PERSON><PERSON><PERSON> sinkt auch die Initiative des Zieles."}, "trailblaze": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender greift an, als würde er aus hohem Gras hervorspringen. Mit wendigen Schritten erhöht er seine Initiative."}, "chillingWater": {"name": "<PERSON><PERSON><PERSON>", "effect": "Der Anwender greift an, indem er das Ziel mit eiskaltem Wasser überschüttet. Das raubt dem Ziel seinen Kampfgeist und senkt so seinen Angriff."}, "hyperDrill": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender lässt einen spitzen Teil seines Körpers rasant rotieren, sticht zu und durchbricht dabei auch die Wirkung von Attacken wie Schutzschild und Scanner."}, "twinBeam": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender greift mit übernatürlichen Lichtstrahlen an, die er aus seinen Augen abfeuert, und trifft das Ziel zweimal hintereinander."}, "rageFist": {"name": "Zornesfaust", "effect": "<PERSON> Angriff, für den der Anwender seinen Zorn in Energie umwandelt. Je häufiger der Anwender getroffen wurde, desto stärker wird diese Attacke."}, "armorCannon": {"name": "Rüstungskanone", "effect": "Der Anwender schießt die eigene Rüstung als glühendes Projektil auf das Ziel. Dadurch sinken die Verteidigung und Spezial-Verteidigung des Anwenders."}, "bitterBlade": {"name": "<PERSON><PERSON>chwer<PERSON>", "effect": "Der Anwender tränkt seine Klinge in Bedauern und Reue und greift damit an. Die Hälfte des zugefügten Schadens wird dem Anwender als KP gutgeschrieben."}, "doubleShock": {"name": "Zweifachladung", "effect": "Der Anwender nutzt die gesamte Elektrizität in seinem Körper, um großen Schaden auszuteilen. Die restliche Kampfdauer gehört er nicht mehr dem Typ Elektro an."}, "gigatonHammer": {"name": "Riesenhammer", "effect": "Der Anwender greift mit einem großen Hammer an, den er mit vollem Körpereinsatz um sich schwingt. Diese Attacke kann nicht zweimal in Folge eingesetzt werden."}, "comeuppance": {"name": "Vendetta", "effect": "Der Anwender rächt sich an dem <PERSON>ner, der ihm zuletzt mit einer Attacke Schaden zugefügt hat, indem er ihm den Schaden mit erhöhter Kraft zurückzahlt."}, "aquaCutter": {"name": "Aquaschnitt", "effect": "Der Anwender stößt Was<PERSON> unter Druck aus, um das Ziel wie mit einer Klinge anzugreifen. Hohe Volltrefferquote."}, "blazingTorque": {"name": "Hitzeturbo", "effect": "Der Anwender rammt seinen glühend heißen Motor in das Ziel. Dieses erleidet eventuell Verbrennungen."}, "wickedTorque": {"name": "Finsterturbo", "effect": "Der Anwender rammt seinen Motor mit böswilliger Absicht in das Ziel. Dieses schläft eventuell ein."}, "noxiousTorque": {"name": "Toxiturbo", "effect": "Der Anwender rammt seinen giftigen Motor in das Ziel. Dieses wird eventuell vergiftet."}, "combatTorque": {"name": "Raufturbo", "effect": "Der Anwender rammt seinen Motor gewaltvoll in das Ziel. Dieses wird eventuell paralysiert."}, "magicalTorque": {"name": "Zauberturbo", "effect": "Der Anwender rammt seinen feenartigen Motor in das Ziel. Dieses wird eventuell verwirrt."}, "bloodMoon": {"name": "<PERSON><PERSON><PERSON>", "effect": "Der Anwender entfesselt eine gewaltige Energieladung aus einem blutroten Vollmond. Diese Attacke kann nicht zweimal in Folge eingesetzt werden."}, "matchaGotcha": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender verschießt gequirlten Tee. Die Hälfte des zugefügten Schadens wird ihm als KP gutgeschrieben. Das Ziel erleidet eventuell Verbrennungen."}, "syrupBomb": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender feuert eine klebrige Sirupbombe auf das Ziel, wodurch es in Sirup gehüllt und seine Initiative drei Runden in Folge gesenkt wird."}, "ivyCudgel": {"name": "Rankenkeule", "effect": "Der Anwender schlägt mit einer rankenumschlungenen Keule zu. Der Typ dieser Attacke hängt von der Maske des Anwenders ab. Hohe Volltrefferquote."}, "electroShot": {"name": "<PERSON>rom<PERSON><PERSON>", "effect": "Sammelt in Runde 1 Elektrizität, um den Spezial-Angriff zu erhöhen, und greift dann in Runde 2 mit Starkstrom an. Bei Regen erfolgt der Angriff sofort in Runde 1."}, "teraStarstorm": {"name": "<PERSON><PERSON><PERSON>", "effect": "Der Anwender greift das Ziel mit gebündelter Kristallenergie an. <PERSON><PERSON>ago<PERSON> diese Attacke in seiner Stellarform einsetzt, erleiden alle Gegner Schaden."}, "fickleBeam": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender greift mit einem Laserstrahl an. Manch<PERSON> feuern mehrere seiner Köpfe Laser ab, wodurch sich die Stärke dieser Attacke verdoppelt."}, "burningBulwark": {"name": "Flammenschild", "effect": "Das brennend heiße Fell des Anwenders schützt ihn vor Angriffen. Gleichzeitig erleiden alle Pokémon, die mit ihm in Berührung kommen, Verbrennungen."}, "thunderclap": {"name": "Sturmblitz", "effect": "<PERSON><PERSON> dieser Erstschlag-Attacke lässt der Anwender einen Blitz auf das Ziel einschlagen. Sie gelingt nur, wenn dieses gerade eine Angriffsattacke vorbereitet."}, "mightyCleave": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender führt mit dem in seinem Kopf gespeicherten Licht einen Schnitt aus. Diese Attacke trifft auch, wenn das Ziel sich selbst schützt."}, "tachyonCutter": {"name": "Tachyon-<PERSON><PERSON><PERSON>", "effect": "Der Anwender greift das Ziel zweimal hintereinander mit Partikelklingen an. Der Angriff trifft garantiert."}, "hardPress": {"name": "Stahlpresse", "effect": "Der Anwender nimmt das Ziel mit Armen oder Scheren in die Mangel. Je höher die KP des Zieles, desto stärker die Attacke."}, "dragonCheer": {"name": "Drachenschrei", "effect": "Das anspornende Drachengebrüll hebt die Moral aller Mitstreiter und erhöht ihre Volltrefferquote. Der Effekt ist stärker, wenn sie dem Typ Drache angehören."}, "alluringVoice": {"name": "Lockstimme", "effect": "Der Anwender greift mit engelsgleichem Gesang an. Falls die Statuswerte des Zieles in dieser Runde erhöht wurden, wird es zusätzlich verwirrt."}, "temperFlare": {"name": "Frustflamme", "effect": "Der Anwender greift das Ziel voller Verzweiflung an. Wenn seine vorige Attacke fehlgeschlagen ist, verdoppelt sich die Stärke der Attacke."}, "supercellSlam": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Der Anwender lädt seinen Körper mit elektrischer Energie auf und stürzt sich auf das Ziel. Bei Misserfolg verletzt sich der Anwender selbst."}, "psychicNoise": {"name": "Psycholärm", "effect": "Der Anwender greift mit unerträglichen Schallwellen an, wodurch das Ziel zwei Runden lang nicht durch Attacken, Fähigkeiten oder getragene Items geheilt werden kann."}, "upperHand": {"name": "Schnellkonter", "effect": "Der Anwender reagiert auf Bewegungen des Zieles und lässt es durch einen Schlag zurückschrecken. <PERSON><PERSON><PERSON> nur, wenn das Ziel gerade eine Erstschlag-Attacke vorbereitet."}, "malignantChain": {"name": "Giftkettung", "effect": "Der Anwender umwickelt das Ziel mit einer Kette aus Toxinen, die in dessen Körper eindringen und ihm schaden. Das Ziel wird eventuell schwer vergiftet."}}