{"cancel": "Abbrechen", "continue": "Fortfahren", "dailyRun": "Täglicher Run", "runHistory": "Laufhistorie", "loadGame": "Spiel laden", "newGame": "Neues Spiel", "settings": "Einstellungen", "selectGameMode": "<PERSON><PERSON><PERSON><PERSON> einen S<PERSON>lmodus", "logInOrCreateAccount": "<PERSON>de dich an oder erstelle einen Account zum starten. <PERSON><PERSON> Email n<PERSON>g!", "username": "<PERSON><PERSON><PERSON><PERSON>", "password": "Passwort", "login": "Anmelden", "loginBeta": "Anmelden (Beta)", "orUse": "<PERSON><PERSON> nut<PERSON>", "register": "Registrieren", "changePassword": "Passwort ändern", "emptyUsername": "<PERSON><PERSON><PERSON><PERSON> darf nicht leer sein.", "invalidLoginUsername": "Der eingegebene Benutzername ist ungültig.", "invalidRegisterUsername": "Benutzername darf nur Buchstaben, Zahlen oder Unterstriche enthalten.", "invalidLoginPassword": "Das eingegebene Passwort ist ungültig.", "invalidRegisterPassword": "Passwort muss 6 <PERSON>eichen oder länger sein.", "usernameAlreadyUsed": "Der eingegebene Benutzername wird bereits verwendet.", "accountNonExistent": "Der eingegebene Benutzer existiert nicht.", "unmatchingPassword": "Das eingegebene Passwort stimmt nicht überein.", "passwordNotMatchingConfirmPassword": "Passwort muss mit Bestätigungspasswort übereinstimmen.", "confirmPassword": "Bestätige Passwort", "registrationAgeWarning": "Mit der Registrierung bestätigen Sie, dass Sie 13 Jahre oder älter sind.", "backToLogin": "Zurück zur Anmeldung", "failedToLoadSaveData": "S<PERSON><PERSON>rda<PERSON> konnten nicht geladen werden. Bitte laden Si<PERSON> die Seite neu.\nÜberprüfe den #announcements-Kanal im Discord bei anhaltenden Problemen.", "serverCommunicationFailed": "Kommunikation mit dem Server fehlgeschlagen.\nDas Spiel läd jetzt neu.", "sessionSuccess": "Sitzung erfolgreich geladen.", "failedToLoadSession": "Ihre Sitzungsdaten konnten nicht geladen werden.\nSie könnten beschädigt sein.", "boyOrGirl": "Bist du ein Junge oder ein Mädchen?", "evolving": "Nanu?\n{{pokemon<PERSON>ame}} entwickelt sich!", "stoppedEvolving": "Hm? {{pokemon<PERSON>ame}} hat die Entwicklung \nabgebrochen.", "pauseEvolutionsQuestion": "Die Entwicklung von {{pokemonName}} vorübergehend pausieren?\nEntwicklungen können im Gruppenmenü wieder aktiviert werden.", "evolutionsPaused": "Entwicklung von {{pokemon<PERSON>ame}} pausiert.", "evolutionDone": "Glückwunsch!\nDein {{pokemonName}} entwickelte sich zu {{evolvedPokemonName}}!", "dailyRankings": "Tägliche Rangliste", "weeklyRankings": "Wöchentliche Rangliste", "noRankings": "<PERSON><PERSON>", "positionIcon": "#", "usernameScoreboard": "<PERSON><PERSON><PERSON><PERSON>", "score": "Punkte", "wave": "Welle", "loading": "<PERSON>de…", "loadingAsset": "Lade Asset: {{assetName}}", "playersOnline": "Spieler Online", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "disclaimer": "HAFTUNGSAUSSCHLUSS", "disclaimerDescription": "Dieses Spiel ist ein unfertiges Produkt. Es kann spielbeinträchtigende Fehler (bis hin zum Verlust des Speicherstandes)\n auf<PERSON>sen, sich ohne Vorankündigung ändern und es gibt keine Garantie dass es weiterentwickelt oder fertiggestellt wird.", "choosePokemon": "<PERSON><PERSON><PERSON><PERSON> ein Pokémon.", "renamePokemon": "Pokémon umbennenen", "renamerun": "<PERSON><PERSON><PERSON><PERSON>", "rename": "Umbenennen", "renameHelpEmoji": "Verwende \"/\" um die Emoji-Bibliothek zu öffnen und gib eine Zahl ein, um ein Emoji auszuwählen (z.B. \"/1\" für das erste Emoji, max. 6)", "nickname": "Spitzname", "runName": "Durchlaufname", "errorServerDown": "Ups! Es gab einen Fehler beim Versuch\nden Server zu kontaktieren\nLasse dieses Fenster offen\nDu wirst automatisch neu verbunden.", "noSaves": "Du hast keine gespeicherten Dateien!", "tooManySaves": "Du hast zu viele gespeicherte Dateien!", "eventTimer": "Event endet in {{days}}:{{hours}}:{{mins}}:{{secs}}"}